package com.gw.agent.controller;

import com.gw.agent.service.*;
import com.gw.agent.vo.AgentTypeStatisticsVO;
import com.gw.agent.vo.DailyStatisticsVO;
import com.gw.agent.vo.MonthlyStatisticsVO;
import com.gw.agent.vo.PlatformStatisticsVO;
import com.gw.common.dto.ResponseResult;
import com.gw.common.membership.service.MembershipProxyService;
import com.gw.common.membership.vo.MembershipStatisticsVO;
import com.gw.common.user.service.UserProxyService;
import com.gw.common.user.vo.UserStatisticsVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 统计控制器
 */
@RestController
@RequestMapping("/api/v1/agent/statistics")
@RequiredArgsConstructor
@Tag(name = "统计管理", description = "统计相关API")
@Log4j2
public class StatisticsController {

    private final AgentService agentService;
    private final AgentUsageRecordService agentUsageRecordService;
    private final UserProxyService userProxyService;
    private final MembershipProxyService membershipProxyService;
    private final UserStatisticsService userStatisticsService;
    private final AgentStatisticsService agentStatisticsService;
    private final AgentStoryStatisticsService agentStoryStatisticsService;

    /**
     * 获取平台统计数据
     */
    @GetMapping("/platform")
    @Operation(
            summary = "获取平台统计数据",
            description = "获取平台各项统计数据",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "统计数据获取成功",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = PlatformStatisticsVO.class)
                            )
                    )
            }
    )
    public ResponseResult<PlatformStatisticsVO> getPlatformStatistics() {
        log.info("开始获取平台统计数据");

        // 获取今日时间范围
        LocalDateTime todayStart = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        log.info("统计时间范围: {} 至 {}", todayStart, todayEnd);

        // 今日活跃用户数
        int todayActiveUsers = agentUsageRecordService.countTodayActiveUsers(todayStart, todayEnd);
        log.info("今日活跃用户数: {}", todayActiveUsers);

        // 今日新增智能体数
        int todayNewAgents = agentService.countTodayNewAgents(todayStart, todayEnd);
        log.info("今日新增智能体数: {}", todayNewAgents);

        // 今日收款金额
        double todayIncome = membershipProxyService.calculateIncomeByTimeRange(todayStart, todayEnd);
        log.info("今日收款金额: {}", todayIncome);

        // 注册用户总数
        UserStatisticsVO vo = userProxyService.getUserStatistics();
        log.info("用户统计数据: 总用户数={}, 今日新增={}, 今日活跃={}", 
                vo.getTotalUsers(), vo.getTodayNewUsers(), vo.getTodayActiveUsers());

        // 获取会员统计数据
        MembershipStatisticsVO membershipStats = membershipProxyService.getMembershipStatistics();
        log.info("会员统计数据: VIP用户数={}, 历史收款总额={}", 
                membershipStats.getTotalVipUsers(), membershipStats.getTotalIncome());

        // VIP用户总数
        int totalVipUsers = membershipStats.getTotalVipUsers();

        // 智能体总数
        int totalAgents = agentService.countTotalAgents();
        log.info("智能体总数: {}", totalAgents);

        // 构建VO对象
        PlatformStatisticsVO platformStatistics = PlatformStatisticsVO.builder()
                .todayActiveUsers(vo.getTodayActiveUsers())
                .todayNewUsers(vo.getTodayNewUsers())
                .todayNewAgents(todayNewAgents)
                .todayIncome(todayIncome)
                .totalUsers(vo.getTotalUsers())
                .totalVipUsers(totalVipUsers)
                .totalAgents(totalAgents)
                .totalIncome(membershipStats.getTotalIncome())
                .build();

        log.info("平台统计数据构建完成: {}", platformStatistics);
        return ResponseResult.success(platformStatistics);
    }

    /**
     * 获取每种智能体类型的数量统计
     */
    @GetMapping("/agent-types")
    @Operation(
            summary = "获取每种智能体类型的数量统计",
            description = "获取每种智能体类型的数量统计数据",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "类型统计数据获取成功",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = AgentTypeStatisticsVO.class)
                            )
                    )
            }
    )
    public ResponseResult<List<AgentTypeStatisticsVO>> getAgentTypeStatistics() {
        List<AgentTypeStatisticsVO> statisticsList = agentService.countAgentsByType();
        return ResponseResult.success(statisticsList);
    }

    /**
     * 获取过去12个月的每月统计数据
     */
    @GetMapping("/monthly")
    @Operation(
            summary = "获取过去12个月的每月统计数据",
            description = "查询过去12个月，每个月新增注册人数，以及每月的活跃数",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "月度统计数据获取成功",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = MonthlyStatisticsVO.class)
                            )
                    )
            }
    )
    public ResponseResult<List<MonthlyStatisticsVO>> getMonthlyStatistics() {
        List<MonthlyStatisticsVO> statistics = userStatisticsService.getMonthlyStatistics(12);
        return ResponseResult.success(statistics);
    }

    /**
     * 获取过去30天的每日统计数据
     */
    @GetMapping("/daily")
    @Operation(
            summary = "获取过去30天的每日统计数据",
            description = "查询过去30天，每天的新增注册人数，以及每天的活跃用户数",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "日度统计数据获取成功",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = DailyStatisticsVO.class)
                            )
                    )
            }
    )
    public ResponseResult<List<DailyStatisticsVO>> getDailyStatistics() {
        List<DailyStatisticsVO> statistics = userStatisticsService.getDailyStatistics(30);
        return ResponseResult.success(statistics);
    }

    /**
     * 获取过去12个月内每天的统计数据
     */
    @GetMapping("/monthly-daily")
    @Operation(
            summary = "获取过去12个月内每天的统计数据",
            description = "查询过去12个月内，每天的新增注册人数，以及每天的活跃用户数",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "月度日度统计数据获取成功",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = DailyStatisticsVO.class)
                            )
                    )
            }
    )
    public ResponseResult<List<DailyStatisticsVO>> getMonthlyDailyStatistics() {
        List<DailyStatisticsVO> statistics = userStatisticsService.getMonthlyDailyStatistics(12);
        return ResponseResult.success(statistics);
    }

    /**
     * 清理重复的智能体统计记录
     */
    @PostMapping("/admin/cleanup/agent-statistics")
    @Operation(
            summary = "清理重复的智能体统计记录",
            description = "合并并清理重复的智能体统计记录，返回清理的记录数量",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "清理完成",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = Integer.class)
                            )
                    )
            }
    )
    public ResponseResult<Integer> cleanupAgentStatistics() {
        log.info("Admin cleanup request for agent statistics duplicates");
        int cleanedCount = agentStatisticsService.cleanupDuplicateRecords();
        return ResponseResult.success(cleanedCount);
    }

    /**
     * 清理重复的剧情统计记录
     */
    @PostMapping("/admin/cleanup/story-statistics")
    @Operation(
            summary = "清理重复的剧情统计记录",
            description = "合并并清理重复的剧情统计记录，返回清理的记录数量",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "清理完成",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = Integer.class)
                            )
                    )
            }
    )
    public ResponseResult<Integer> cleanupStoryStatistics() {
        log.info("Admin cleanup request for story statistics duplicates");
        int cleanedCount = agentStoryStatisticsService.cleanupDuplicateRecords();
        return ResponseResult.success(cleanedCount);
    }
}