package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.agent.entity.AgentStatisticsEntity;
import com.gw.agent.mapper.sql.AgentStatisticsMapper;
import com.gw.agent.service.AgentStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Log4j2
public class AgentStatisticsServiceImpl extends ServiceImpl<AgentStatisticsMapper, AgentStatisticsEntity> implements AgentStatisticsService {
    @Override
    public void insert(AgentStatisticsEntity agentStatisticsEntity) {
        this.baseMapper.insert(agentStatisticsEntity);
    }

    @Override
    public void updateFavoriteCount(Long agentId, Integer increment) {
        AgentStatisticsEntity entity = this.baseMapper.findFirstByAgentId(agentId);
        if (entity != null) {
            entity.setFavoriteCount(entity.getFavoriteCount() + increment);
            if (entity.getFavoriteCount() < 0) {
                entity.setFavoriteCount(0);
            }
            this.baseMapper.updateFavoriteCount(entity.getId(), entity.getFavoriteCount());
        } else {
            entity = new AgentStatisticsEntity(agentId);
            if (increment > 0) {
                entity.setFavoriteCount(increment);
            }
            this.baseMapper.insert(entity);
        }
    }

    @Override
    public void updateLikeCount(Long agentId, Integer increment) {
        AgentStatisticsEntity entity = this.baseMapper.findFirstByAgentId(agentId);
        if (entity != null) {
            entity.setLikeCount(entity.getLikeCount() + increment);
            if (entity.getLikeCount() < 0) {
                entity.setLikeCount(0);
            }
            this.baseMapper.updateLikeCount(entity.getId(), entity.getLikeCount());
        } else {
            entity = new AgentStatisticsEntity(agentId);
            if (increment > 0) {
                entity.setLikeCount(increment);
            }
            this.baseMapper.insert(entity);
        }
    }

    @Override
    public void updateCommentCount(Long agentId, Integer increment) {
        AgentStatisticsEntity entity = this.baseMapper.findFirstByAgentId(agentId);
        if (entity != null) {
            entity.setCommentCount(entity.getCommentCount() + increment);
            if (entity.getCommentCount() < 0) {
                entity.setCommentCount(0);
            }
            this.baseMapper.updateCommentCount(entity.getId(), entity.getCommentCount());
        } else {
            entity = new AgentStatisticsEntity(agentId);
            if (increment > 0) {
                entity.setCommentCount(increment);
            }
            this.baseMapper.insert(entity);
        }
    }

    @Override
    public Map<Long, AgentStatisticsEntity> findAllGroupByAgentId() {
        return this.baseMapper.findAll().stream().collect(Collectors.toMap(
                AgentStatisticsEntity::getAgentId,
                Function.identity(),
                // Merge function: when duplicate keys exist, combine the statistics
                (existing, replacement) -> {
                    log.warn("Found duplicate agentId: {}, combining statistics. Existing: {}, Replacement: {}",
                            existing.getAgentId(), existing, replacement);

                    // Create a new entity with combined statistics
                    AgentStatisticsEntity merged = new AgentStatisticsEntity();
                    merged.setId(Math.max(existing.getId(), replacement.getId())); // Use the higher ID
                    merged.setAgentId(existing.getAgentId());
                    merged.setFavoriteCount(existing.getFavoriteCount() + replacement.getFavoriteCount());
                    merged.setCommentCount(existing.getCommentCount() + replacement.getCommentCount());
                    merged.setLikeCount(existing.getLikeCount() + replacement.getLikeCount());

                    return merged;
                }
        ));
    }

    @Override
    public AgentStatisticsEntity findByAgentId(Long agentId) {
        return this.baseMapper.findFirstByAgentId(agentId);
    }

    @Override
    public int cleanupDuplicateRecords() {
        log.info("Starting cleanup of duplicate AgentStatistics records");

        List<AgentStatisticsEntity> allRecords = this.baseMapper.findAll();
        Map<Long, List<AgentStatisticsEntity>> groupedByAgentId = allRecords.stream()
            .collect(Collectors.groupingBy(AgentStatisticsEntity::getAgentId));

        int cleanedUpCount = 0;

        for (Map.Entry<Long, List<AgentStatisticsEntity>> entry : groupedByAgentId.entrySet()) {
            List<AgentStatisticsEntity> duplicates = entry.getValue();
            if (duplicates.size() > 1) {
                log.info("Found {} duplicate records for agentId: {}", duplicates.size(), entry.getKey());

                // Sort by ID descending to keep the latest record
                duplicates.sort((a, b) -> Long.compare(b.getId(), a.getId()));

                // Combine all statistics into the latest record
                AgentStatisticsEntity latest = duplicates.get(0);
                int totalFavorites = duplicates.stream().mapToInt(AgentStatisticsEntity::getFavoriteCount).sum();
                int totalComments = duplicates.stream().mapToInt(AgentStatisticsEntity::getCommentCount).sum();
                int totalLikes = duplicates.stream().mapToInt(AgentStatisticsEntity::getLikeCount).sum();

                // Update the latest record with combined statistics
                this.baseMapper.updateFavoriteCount(latest.getId(), totalFavorites);
                this.baseMapper.updateLikeCount(latest.getId(), totalLikes);
                this.baseMapper.updateCommentCount(latest.getId(), totalComments);

                // Delete the older duplicate records
                for (int i = 1; i < duplicates.size(); i++) {
                    this.baseMapper.deleteById(duplicates.get(i).getId());
                    cleanedUpCount++;
                    log.info("Deleted duplicate record with id: {} for agentId: {}",
                        duplicates.get(i).getId(), entry.getKey());
                }

                log.info("Consolidated statistics for agentId: {} - Favorites: {}, Comments: {}, Likes: {}",
                    entry.getKey(), totalFavorites, totalComments, totalLikes);
            }
        }

        log.info("Cleanup completed. Removed {} duplicate records", cleanedUpCount);
        return cleanedUpCount;
    }

}
