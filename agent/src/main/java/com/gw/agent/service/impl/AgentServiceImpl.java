package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gw.agent.config.CacheProperties;
import com.gw.agent.constant.AgentCacheConstant;
import com.gw.agent.dto.AgentQueryDTO;
import com.gw.agent.dto.MyCommentAgentQueryDTO;
import com.gw.agent.dto.MyFavoriteAgentQueryDTO;
import com.gw.agent.dto.MyLikeAgentQueryDTO;
import com.gw.agent.entity.*;
import com.gw.agent.event.SecurityCheckPassedEvent;
import com.gw.agent.mapper.ModelMapperConvert;
import com.gw.agent.mapper.sql.AgentMapper;
import com.gw.agent.mapper.sql.AgentTagRelationMapper;
import com.gw.agent.mapper.sql.MyAgentCommonSettingMapper;
import com.gw.agent.service.*;
import com.gw.agent.vo.AgentTypeStatisticsVO;
import com.gw.common.agent.constant.AgentCommonCacheConstant;
import com.gw.common.agent.constant.AgentConstant;
import com.gw.common.agent.constant.AgentStatus;
import com.gw.common.agent.service.AgentProxyService;
import com.gw.common.agent.vo.AgentBaseVO;
import com.gw.common.dto.PageOrderField;
import com.gw.common.exception.BusinessException;
import com.gw.common.exception.EntityNotFoundException;
import com.gw.common.notify.constant.NotifyConstant;
import com.gw.common.notify.dto.InteractiveNotifySubmitDTO;
import com.gw.common.notify.dto.SystemNotifySubmitDTO;
import com.gw.common.notify.service.NotifyProxyService;
import com.gw.common.util.ImageUtils;
import com.gw.common.util.UploadFileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Log4j2
public class AgentServiceImpl extends ServiceImpl<AgentMapper, AgentEntity> implements AgentService {

    private final AgentStatisticsService statisticsService;
    private final AgentTagRelationMapper agentTagRelationMapper;
    private final AgentTagService agentTagService;
    private final AgentLikeService agentLikeService;
    private final SecurityCheckService securityCheckService;
    private final AgentRemoteService agentRemoteService;
    private final AgentTypeService agentTypeService;
    private final AgentUsageRecordService agentUsageRecordService;
    private final MyAgentCommonSettingMapper settingMapper;
    private final CacheManager cacheManager;
    private final NotifyProxyService notifyProxyService;
    private final CacheProperties cacheProperties;
    private final AgentProxyService agentProxyService;
    private final String CacheCntValue = "agentCnt";
    private final AgentCommentService agentCommentService;
    private final AgentFavoriteService agentFavoriteService;
    private final AgentDailyUsageRecordService agentDailyUsageRecordService;
    @Qualifier("taskExecutor")
    private final ThreadPoolTaskExecutor taskExecutor;
    private final Object cacheLock = new Object();
    private final AgentStoryService agentStoryService;
    /**
     * 缩略图宽度
     */
    @Value("${agent.thumbnail.width:375}")
    private int thumbnailWidth;
    /**
     * 缩略图高度
     */
    @Value("${agent.thumbnail.height:812}")
    private int thumbnailHeight;
    /**
     * 缩略图质量因子
     */
    @Value("${agent.thumbnail.quality:0.5}")
    private float thumbnailQuality;

    /**
     * 更新智能体缓存
     * 集中处理缓存更新逻辑，确保一致性
     */
    private void updateAgentCache(AgentEntity entity) {
        if (entity == null || entity.getId() == null) {
            return;
        }

        // 更新本地缓存
        Cache cache = cacheManager.getCache(AgentCacheConstant.AGENT_CACHE);
        if (cache != null) {
            cache.put("id:" + entity.getId(), entity);
        }

        // 更新Redis缓存
        refreshCache(entity);
    }

    /**
     * 更新Redis缓存
     */
    private void refreshCache(AgentEntity entity) {
        if (entity == null || entity.getId() == null) {
            return;
        }
        synchronized (cacheLock) {
            entity = findById(entity.getId());
            String cacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_MAP_CACHE_KEY);
            ModelMapper modelMapper = ModelMapperConvert.getAgentBaseModelMapper();
            AgentBaseVO vo = modelMapper.map(entity, AgentBaseVO.class);
            var duration = cacheProperties.getCacheExpiration(AgentCommonCacheConstant.AGENT_BASE_KEY);
            log.info("更新缓存：{}", cacheKey);
            agentProxyService.updateAgentBaseToCacheIfCacheExist(cacheKey, entity.getId(), vo, duration);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = CacheCntValue, key = "'ac:' + #entity.creator"),
            @CacheEvict(value = AgentCacheConstant.AGENT_ALL_CACHE, allEntries = true),
            @CacheEvict(value = AgentCacheConstant.AGENT_CACHE, key = "'id:' + #entity.id"),
            @CacheEvict(value = AgentCacheConstant.AGENT_PAGE_CACHE, allEntries = true)
    })
    public void insert(AgentEntity entity) {
        log.info("开始创建智能体: {}", entity.getName());
        entity.setStatus(AgentStatus.CHECKING.getCode());
        this.baseMapper.insert(entity);
        saveAgentTagRelations(entity);
        securityCheckService.performSecurityCheck(entity);

        refreshCache(entity);
        this.statisticsService.insert(new AgentStatisticsEntity(entity.getId()));
    }

    @EventListener
    public void handleSecurityCheckPassedEvent(SecurityCheckPassedEvent event) {
        AgentEntity entity = event.getEntity();
        if(event.isFailed() && event.isUpdate()){
            agentStoryService.modifyShelfStatusOffByAgentId(entity.getId(), entity.getName() + "角色审核失败", "admin");
            return;
        }
        if (event.isUpdate()) {
            log.info("收到安全检查通过事件，开始更新远程智能体: {}", entity.getId());
            updateAgentAfterSecurityCheck(entity);

        } else {
            log.info("收到安全检查通过事件，开始创建远程智能体: {}", entity.getId());
            createAgentAfterSecurityCheck(entity);
        }

    }

    public void createAgentAfterSecurityCheck(AgentEntity entity) {
        processAgentAfterSecurityCheck(entity, false);
    }

    public void updateAgentAfterSecurityCheck(AgentEntity entity) {
        processAgentAfterSecurityCheck(entity, true);
    }

    /**
     * 统一处理安全检查通过后的智能体创建或更新流程
     *
     * @param entity   智能体实体
     * @param isUpdate 是否为更新操作
     */
    private void processAgentAfterSecurityCheck(AgentEntity entity, boolean isUpdate) {
        String operationType = isUpdate ? "更新" : "创建";
        log.info("安全检查通过，开始{}远程智能体: {}", operationType, entity.getName());
        if (entity.getPlatform() == AgentConstant.HUO_SHAN_PLATFORM) {
            updateAgentStatus(entity, AgentStatus.PUBLISHED);
            return;
        }
        try {
            updateAgentStatus(entity, AgentStatus.CREATING);

            if (isUpdate) {
                AgentEntity originalEntity = getAgentById(entity.getId());
                if (StringUtils.hasText(originalEntity.getRemoteBotId())) {
                    // 更新已存在的远程智能体
                    processExistingRemoteAgent(entity, originalEntity);
                } else {
                    // 对于没有远程ID的情况，创建新的远程智能体
                    processNewRemoteAgent(entity);
                }
            } else {
                // 创建新的远程智能体
                processNewRemoteAgent(entity);
            }
        } catch (Exception e) {
            log.error("调用Coze API{}智能体失败: {} {}", operationType, e.getMessage(), e);
            updateAgentStatus(entity, AgentStatus.CREATE_FAILED);
            notifyProxyService.insertSystemMessage(new SystemNotifySubmitDTO("你创建的" + entity.getName() + "发布失败！",
                    NotifyConstant.LEVEL_NORMAL, entity.getId(), entity.getCreator()));
            throw new BusinessException(operationType + "智能体失败: " + e.getMessage());
        }
    }

    /**
     * 处理已存在远程ID的智能体更新
     *
     * @param entity         当前智能体实体
     * @param originalEntity 原始智能体实体
     */
    private void processExistingRemoteAgent(AgentEntity entity, AgentEntity originalEntity) {
        // 确保有远程ID
        if (!StringUtils.hasText(entity.getRemoteBotId())) {
            entity.setRemoteBotId(originalEntity.getRemoteBotId());
        }

        // 更新远程智能体
        agentRemoteService.updateAgent(entity);
        updateAgentStatus(entity, AgentStatus.PUBLISHING);

        // 发布智能体
        publishRemoteAgent(entity, entity.getRemoteBotId(), true);
        notifyProxyService.insertSystemMessage(new SystemNotifySubmitDTO("角色消息", "你创建的" + entity.getName() + "发布成功！",
                NotifyConstant.LEVEL_NORMAL, entity.getId(), entity.getCreator()));
    }

    /**
     * 处理创建新的远程智能体
     *
     * @param entity 智能体实体
     */
    private void processNewRemoteAgent(AgentEntity entity) {
        AgentEntity updatedEntity = agentRemoteService.createAgent(entity);

        if (StringUtils.hasText(updatedEntity.getRemoteBotId())) {
            updateAgentStatusAndBotId(entity, updatedEntity.getRemoteBotId());
            publishRemoteAgent(entity, updatedEntity.getRemoteBotId(), false);
            notifyProxyService.insertSystemMessage(new SystemNotifySubmitDTO("角色消息", "你创建的" + entity.getName() + "发布成功！",
                    NotifyConstant.LEVEL_NORMAL, entity.getId(), entity.getCreator()));
        } else {
            log.warn("创建远程智能体失败：未获取到远程ID");
            updateAgentStatus(entity, AgentStatus.CREATE_FAILED);
            notifyProxyService.insertSystemMessage(new SystemNotifySubmitDTO("你创建的" + entity.getName() + "发布失败！",
                    NotifyConstant.LEVEL_NORMAL, entity.getId(), entity.getCreator()));
        }
    }

    /**
     * 发布远程智能体
     *
     * @param entity      智能体实体
     * @param remoteBotId 远程智能体ID
     * @param isUpdate    是否为更新操作
     */
    private void publishRemoteAgent(AgentEntity entity, String remoteBotId, boolean isUpdate) {
        boolean publishSuccess = agentRemoteService.publicAgent(entity);
        AgentStatus newStatus = publishSuccess ? AgentStatus.PUBLISHED : AgentStatus.PUBLISH_FAILED;
        entity.setStatus(newStatus.getCode());
        // 更新状态
        if (publishSuccess) {
            entity.setRemoteBotId(remoteBotId);
            this.baseMapper.updateStatusAndBotID(entity.getId(), newStatus.getCode(), remoteBotId);
        } else {
            updateAgentStatus(entity, newStatus);
        }
        refreshCache(entity);

        // 记录日志
        log.info("{}并发布智能体{}: {}, 状态: {}",
                isUpdate ? "更新" : "创建",
                publishSuccess ? "成功" : "失败",
                remoteBotId,
                newStatus.getDescription());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = CacheCntValue, key = "'ac:' + #entity.creator"),
            @CacheEvict(value = AgentCacheConstant.AGENT_CACHE, key = "'id:' + #entity.id"),
            @CacheEvict(value = AgentCacheConstant.AGENT_ALL_CACHE, allEntries = true),
            @CacheEvict(value = AgentCacheConstant.AGENT_PAGE_CACHE, allEntries = true)
    })
    public void update(AgentEntity entity) {
        log.info("开始更新智能体: {}", entity.getName());

        // 设置更新时间和更新人
        entity.setUpdateTime(LocalDateTime.now());
        if (entity.getUpdater() == null) {
            entity.setUpdater(entity.getCreator()); // 使用创建者作为更新者
        }
        entity.setStatus(AgentStatus.CHECKING.getCode());

        // 更新数据库
        boolean success = this.baseMapper.updateById(entity) > 0;
        if (!success) {
            throw new BusinessException("更新智能体失败");
        }

        // 更新标签关系
        updateAgentTagRelations(entity);

        // 调用安全检查服务
        securityCheckService.performSecurityCheck(entity);
        cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_MAP_CACHE_KEY);
        refreshCache(entity);
    }

    @Override
    @Caching(evict = {
            @CacheEvict(value = CacheCntValue, key = "'ac:' + #entity.creator"),
            @CacheEvict(value = AgentCacheConstant.AGENT_CACHE, key = "'id:' + #entity.id"),
            @CacheEvict(value = AgentCacheConstant.AGENT_ALL_CACHE, allEntries = true),
            @CacheEvict(value = AgentCacheConstant.AGENT_PAGE_CACHE, allEntries = true)
    })
    public void updateNotPublic(AgentEntity entity) {
        this.baseMapper.updateById(entity);
        refreshCache(entity);
    }

    /**
     * 更新智能体标签关系
     */
    private void updateAgentTagRelations(AgentEntity entity) {
        if (entity == null || entity.getId() == null) {
            return;
        }

        try {
            // 使用事务确保原子操作
            deleteAgentTagRelations(entity.getId());
            if (!CollectionUtils.isEmpty(entity.getTags())) {
                saveAgentTagRelations(entity);
            }
        } catch (Exception e) {
            log.error("更新智能体标签关系失败: {}", e.getMessage());
            throw new BusinessException("更新智能体标签关系失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(value = AgentCacheConstant.AGENT_ALL_CACHE, allEntries = true),
            @CacheEvict(value = AgentCacheConstant.AGENT_PAGE_CACHE, allEntries = true),
            @CacheEvict(value = AgentCacheConstant.AGENT_CACHE, key = "'id:' + #entity.id"),
            @CacheEvict(value = CacheCntValue, key = "'ac:' + #entity.creator")
    })
    public void delete(AgentEntity entity) {
        log.info("开始删除智能体: {}", entity.getId());
        // 删除本地数据
        this.baseMapper.deleteById(entity.getId());
        deleteAgentTagRelations(entity.getId());

        // 如果有远程ID，则删除远程智能体
        deleteRemoteAgentIfNeeded(entity);
        String cacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_MAP_CACHE_KEY);
        agentProxyService.deleteAgentBaseKeyFromCache(cacheKey, entity.getId());
    }

    @Override
    @Cacheable(value = CacheCntValue, key = "'ac:' + #creator")
    public int cntByCreator(String creator) {
        return this.baseMapper.countByCreator(creator);
    }

    /**
     * 如果智能体有远程ID，则删除远程智能体
     */
    private void deleteRemoteAgentIfNeeded(AgentEntity entity) {
        if (StringUtils.hasText(entity.getRemoteBotId())) {
            try {
                boolean result = agentRemoteService.deleteAgent(entity.getRemoteBotId());
                log.info("删除Coze平台智能体结果: {}, cozeAgentId: {}", result, entity.getRemoteBotId());
            } catch (Exception e) {
                log.error("调用Coze API删除智能体失败: {} {}", e.getMessage(), e);
                throw new BusinessException("删除智能体失败: " + e.getMessage());
            }
        }
    }

    /**
     * 根据ID获取智能体，如果不存在则抛出异常
     */
    private AgentEntity getAgentById(Long id) {
        AgentEntity entity = this.baseMapper.selectById(id);
        if (entity == null) {
            throw new EntityNotFoundException("智能体不存在，ID: " + id);
        }
        return entity;
    }

    /**
     * 更新智能体状态
     */
    private void updateAgentStatus(AgentEntity entity, AgentStatus status) {
        if (entity == null || status == null) {
            return;
        }
        try {
            this.baseMapper.updateStatus(entity.getId(), status.getCode());
            entity.setStatus(status.getCode());
            refreshCache(entity);
        } catch (Exception e) {
            log.error("更新智能体状态失败: {}", e.getMessage(), e);
            throw new BusinessException("更新智能体状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新智能体状态和远程ID
     */
    private void updateAgentStatusAndBotId(AgentEntity entity, String remoteBotId) {
        this.baseMapper.updateStatusAndBotID(entity.getId(), AgentStatus.PUBLISHING.getCode(), remoteBotId);
        entity.setRemoteBotId(remoteBotId);
        entity.setStatus(AgentStatus.PUBLISHING.getCode());
        refreshCache(entity);
    }

    @Override
    public AgentEntity findById(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("智能体ID不能为空");
        }
        return this.baseMapper.findById(id).orElseThrow(() -> new EntityNotFoundException("智能体不存在: " + id));
    }

    @Override
    public Boolean existByExclusive() {
        return this.baseMapper.findFirstByExclusive().isPresent();
    }

    @Override
    public AgentEntity findByExclusive() {
        return this.baseMapper.findFirstByExclusive().orElse(null);
    }

    @Override
    public Long findFirstIdByExclusive() {
        return this.baseMapper.findFirstIdByExclusive();
    }

    @Override
    @Cacheable(value = AgentCacheConstant.AGENT_CACHE, key = "'id:' + #id")
    public AgentEntity findById(Long id, String username) {
        AgentEntity entity = this.findById(id);

        // 填充点赞信息
        fillAgentWithIsLikeInfo(entity, username);
        AgentStatisticsEntity statistics = statisticsService.findByAgentId(id);
        entity.setFavoriteCount(statistics == null ? 0 : statistics.getFavoriteCount());
        // 填充评论信息
        entity.setCommentCount(statistics == null ? 0 : statistics.getCommentCount());
        entity.setLikeCount(statistics == null ? 0 : statistics.getLikeCount());
        Map<Long, AgentTagEntity> map = agentTagService.findTagsMap();
        entity.setType(agentTypeService.findById(entity.getTypeId()));
        entity.setTags(new ArrayList<>());
        agentTagRelationMapper.findByAgentId(entity.getId()).forEach(tagRelation -> entity.getTags().add(map.get(tagRelation.getTagId())));
        return entity;
    }

    @Override
    public List<AgentEntity> findAllByCreator(String username) {
        if (!StringUtils.hasText(username)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AgentEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentEntity::getCreator, username);
        queryWrapper.eq(AgentEntity::getDeleted, 0);
        List<AgentEntity> list = this.baseMapper.selectList(queryWrapper);

        if (!CollectionUtils.isEmpty(list)) {
            fillAgentListTags(list);
        }

        return list;
    }

    @Override
    public List<AgentEntity> findAll() {
        return this.baseMapper.findAll();
    }

    @Override
    public List<AgentEntity> findAllByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return this.baseMapper.findAllByIds(ids);
    }

    @Override
    public List<AgentEntity> queryByIdsKeepOrder(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<AgentEntity> agentEntities = this.baseMapper.findAllByIdsNotSort(ids);

        // Create map for O(1) lookup
        Map<Long, AgentEntity> entityMap = agentEntities.stream()
                .collect(Collectors.toMap(AgentEntity::getId, Function.identity()));

        // Return list maintaining original ids order
        return ids.stream()
                .map(entityMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Caching(evict = {
            @CacheEvict(value = AgentCacheConstant.AGENT_ALL_CACHE, allEntries = true),
            @CacheEvict(value = AgentCacheConstant.AGENT_PAGE_CACHE, allEntries = true),
            @CacheEvict(value = AgentCacheConstant.AGENT_CACHE, key = "'id:' + #agentId")
    })
    @Override
    public void updateRecommendIdxById(Long agentId, Integer recommendIdx) {
        this.baseMapper.updateRecommendIdxById(agentId, recommendIdx);
    }

    @Override
    public PageInfo<AgentEntity> page(int pageNum, int pageSize, AgentQueryDTO query, PageOrderField orderFields) {
        long startTime = System.currentTimeMillis();
        log.info("[PAGE-TIMING] 开始分页查询 - pageNum: {}, pageSize: {}, orderFields: {}", pageNum, pageSize, orderFields);

        long paramProcessStart = System.currentTimeMillis();
        if (orderFields.getKey() != null && orderFields.getKey().equalsIgnoreCase("updatetime")) {
            orderFields.setKey("update_time");
        }
        // 规范化参数
        pageNum = Math.max(1, pageNum);
        pageSize = Math.max(1, Math.min(100, pageSize)); // 设置最大页大小限制，避免大查询
        log.info("[PAGE-TIMING] 参数处理完成，耗时: {}ms", System.currentTimeMillis() - paramProcessStart);

        // 直接进行数据库查询，不使用缓存
        PageInfo<AgentEntity> result;
        long dbQueryStart = System.currentTimeMillis();

        try {
            result = loadResultsUsingStandardQuery(pageNum, pageSize, query, orderFields);
            log.info("[PAGE-TIMING] 标准查询流程完成，耗时: {}ms", System.currentTimeMillis() - dbQueryStart);
            log.info("[PAGE-TIMING] 查询智能体分页总耗时: {}ms, 返回记录数: {}", System.currentTimeMillis() - startTime, result.getList().size());
            return result;
        } catch (Exception e) {

            log.error("[PAGE-TIMING] 执行分页查询出错: {}, 耗时: {}ms", e.getMessage(), System.currentTimeMillis() - dbQueryStart, e);

            // 如果查询失败，返回空结果
            PageInfo<AgentEntity> emptyResult = new PageInfo<>(new ArrayList<>());
            emptyResult.setPageNum(pageNum);
            emptyResult.setPageSize(pageSize);
            emptyResult.setTotal(0);
            emptyResult.setPages(0);

            log.info("[PAGE-TIMING] 返回空结果，总耗时: {}ms", System.currentTimeMillis() - startTime);
            return emptyResult;
        }
    }

    /**
     * 使用ID列表优化的方式加载分页结果
     */
    private PageInfo<AgentEntity> loadResultsUsingStandardQuery(int pageNum, int pageSize, AgentQueryDTO query, PageOrderField orderFields) {
        long startTime = System.currentTimeMillis();
        log.info("[STD-TIMING] 开始标准查询流程");

        long dbQueryStart = System.currentTimeMillis();
        PageHelper.startPage(pageNum, pageSize, true);
        List<AgentEntity> list = this.baseMapper.page(query, orderFields);

        log.info("[STD-TIMING] 数据库查询完成，获取到{}条记录，耗时: {}ms", list.size(), System.currentTimeMillis() - dbQueryStart);

        // 批量加载实体 - 使用并行流处理大量数据

        // 保持ID列表原顺序


        // 填充社交信息
        if (!CollectionUtils.isEmpty(list)) {
            long enhanceStart = System.currentTimeMillis();
            fillAgentsWithSocialInfoEfficiently(list, query.getUsername());
            log.info("[STD-TIMING] 社交信息填充完成，耗时: {}ms", System.currentTimeMillis() - enhanceStart);
        }

        long resultBuildStart = System.currentTimeMillis();
        PageInfo<AgentEntity> result = new PageInfo<>(list);
        log.info("[STD-TIMING] 结果构建完成，耗时: {}ms", System.currentTimeMillis() - resultBuildStart);

        log.info("[STD-TIMING] 标准查询全流程完成，总耗时: {}ms", System.currentTimeMillis() - startTime);
        return result;
    }

    /**
     * 并行加载智能体实体列表，适用于大量数据
     */
    private void fillAgentsWithSocialInfoEfficiently(List<AgentEntity> list, String username) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        long startTime = System.currentTimeMillis();
        log.info("[SOCIAL-TIMING] 开始填充社交信息，智能体数量: {}, 用户: {}", list.size(), username);

        final int BATCH_SIZE = 100;
        if (list.size() > BATCH_SIZE) {
            log.info("[SOCIAL-TIMING] 使用分批处理模式，批次大小: {}", BATCH_SIZE);
            // 分批处理以避免数据库压力过大
            for (int i = 0; i < list.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, list.size());

                List<AgentEntity> batch = list.subList(i, endIndex);

                long batchStart = System.currentTimeMillis();
                fillAgentsWithSocialInfoBatch(batch, username);
                log.info("[SOCIAL-TIMING] 批次 {}-{} 处理完成，耗时: {}ms", i, endIndex - 1, System.currentTimeMillis() - batchStart);
            }
            log.info("[SOCIAL-TIMING] 分批处理全部完成，总耗时: {}ms", System.currentTimeMillis() - startTime);
            return;
            // 异步更新缓存
        }

        log.info("[SOCIAL-TIMING] 使用单批处理模式");
        fillAgentsWithSocialInfoBatch(list, username);
        log.info("[SOCIAL-TIMING] 单批处理完成，总耗时: {}ms", System.currentTimeMillis() - startTime);
    }

    /**
     * 批量填充智能体社交信息的核心方法
     * 优化内存使用，减少临时对象创建
     */
    private void fillAgentsWithSocialInfoBatch(List<AgentEntity> list, String username) {
        long startTime = System.currentTimeMillis();
        log.info("[BATCH-TIMING] 开始批量处理社交信息，数量: {}", list.size());

        // 预先计算容量，避免Map扩容
        long prepareStart = System.currentTimeMillis();

        final List<Long> agentIds = list.stream().map(AgentEntity::getId).toList();
        log.info("[BATCH-TIMING] 数据准备完成，有效ID数量: {}, 耗时: {}ms", agentIds.size(), System.currentTimeMillis() - prepareStart);

        if (agentIds.isEmpty()) {
            return;
        }

        try {
            // 使用指定线程池，避免创建过多线程
            final Executor executor = taskExecutor;
            System.currentTimeMillis();

            // 创建异步任务，复用Map容量设置
            CompletableFuture<Map<Long, List<AgentTagEntity>>> tagsFuture =
                    CompletableFuture.supplyAsync(() -> {
                                long tagStart = System.currentTimeMillis();
                                Map<Long, List<AgentTagEntity>> result = fetchAgentTagsInBatch(agentIds);
                                log.info("[BATCH-TIMING] 标签查询完成，耗时: {}ms", System.currentTimeMillis() - tagStart);
                                return result;
                            }, executor)
                            .orTimeout(5, TimeUnit.SECONDS)
                            .exceptionally(ex -> {
                                log.warn("[BATCH-TIMING] 获取智能体标签失败: {}", ex.getMessage());
                                return Collections.emptyMap();
                            });

            CompletableFuture<SocialInfoData> socialInfoFuture =
                    CompletableFuture.supplyAsync(() -> {
                                long socialStart = System.currentTimeMillis();
                                SocialInfoData result = fetchSocialInfoBatch(agentIds, username);
                                log.info("[BATCH-TIMING] 社交数据查询完成，耗时: {}ms", System.currentTimeMillis() - socialStart);
                                return result;
                            }, executor)
                            .orTimeout(8, TimeUnit.SECONDS)
                            .exceptionally(ex -> {
                                log.warn("[BATCH-TIMING] 获取社交信息失败: {}", ex.getMessage());
                                return new SocialInfoData();
                            });

            // 等待所有任务完成
            long waitStart = System.currentTimeMillis();
            CompletableFuture.allOf(tagsFuture, socialInfoFuture)
                    .get(12, TimeUnit.SECONDS);
            log.info("[BATCH-TIMING] 异步任务全部完成，等待耗时: {}ms", System.currentTimeMillis() - waitStart);

            // 获取结果并填充数据
            long fillStart = System.currentTimeMillis();
            final Map<Long, List<AgentTagEntity>> agentTagsMap = tagsFuture.get();
            final Map<Long, AgentTypeEntity> typeMap = agentTypeService.findTypesMap();
            final SocialInfoData socialInfo = socialInfoFuture.get();
            log.info("[BATCH-TIMING] 类型映射获取完成，耗时: {}ms", System.currentTimeMillis() - fillStart);

            // 使用传统for循环替代forEach，减少lambda开销
            long dataFillStart = System.currentTimeMillis();
            for (AgentEntity agent : list) {
                if (agent == null || agent.getId() == null) {
                    continue;
                }

                final Long agentId = agent.getId();

                // 填充类型信息
                if (agent.getTypeId() != null && typeMap.containsKey(agent.getTypeId())) {
                    agent.setType(typeMap.get(agent.getTypeId()));
                }

                // 填充标签信息
                agent.setTags(agentTagsMap.getOrDefault(agentId, Collections.emptyList()));
                AgentStatisticsEntity statisticsEntity = socialInfo.statisticsEntityMap.getOrDefault(agentId, new AgentStatisticsEntity());
                // 填充社交信息
                agent.setLikeCount(statisticsEntity.getLikeCount());
                agent.setCommentCount(statisticsEntity.getCommentCount());
                agent.setFavoriteCount(statisticsEntity.getFavoriteCount());
                agent.setIsLiked(socialInfo.userLikesMap.getOrDefault(agentId, 0));
            }
            log.info("[BATCH-TIMING] 数据填充完成，耗时: {}ms", System.currentTimeMillis() - dataFillStart);
            log.info("[BATCH-TIMING] 批量处理全部完成，总耗时: {}ms", System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            log.error("[BATCH-TIMING] 批量获取智能体社交信息失败: {}, 耗时: {}ms", e.getMessage(), System.currentTimeMillis() - startTime, e);
            // 降级处理：使用同步方式填充基本信息
            long fallbackStart = System.currentTimeMillis();
            fillAgentListTagsSync(list);
            setDefaultSocialInfo(list);
            log.info("[BATCH-TIMING] 降级处理完成，耗时: {}ms", System.currentTimeMillis() - fallbackStart);
        }
    }

    /**
     * 批量获取社交信息，减少异步任务数量
     */
    private SocialInfoData fetchSocialInfoBatch(List<Long> agentIds, String username) {
        try {
            // 并行获取社交数据
            CompletableFuture<Map<Long, AgentStatisticsEntity>> statisticsFuture =
                    CompletableFuture.supplyAsync(statisticsService::findAllGroupByAgentId);

            CompletableFuture<Map<Long, Integer>> userLikesFuture;
            if (StringUtils.hasText(username)) {
                userLikesFuture = CompletableFuture.supplyAsync(() -> agentLikeService.batchIsLiked(agentIds, username));
            } else {
                userLikesFuture = CompletableFuture.completedFuture(Collections.emptyMap());
            }

            // 等待所有任务完成
            CompletableFuture.allOf(statisticsFuture, userLikesFuture)
                    .get(6, TimeUnit.SECONDS);

            return new SocialInfoData(
                    statisticsFuture.get(),
                    userLikesFuture.get()
            );
        } catch (Exception e) {
            log.warn("获取社交信息批量数据失败: {}", e.getMessage());
            return new SocialInfoData();
        }
    }

    /**
     * 同步方式填充标签信息（降级处理）
     */
    private void fillAgentListTagsSync(List<AgentEntity> list) {
        try {
            Map<Long, AgentTagEntity> tagsMap = agentTagService.findTagsMap();
            if (tagsMap.isEmpty()) {
                list.forEach(agent -> agent.setTags(Collections.emptyList()));
                return;
            }

            for (AgentEntity agent : list) {
                if (agent != null && agent.getId() != null) {
                    List<AgentTagRelationEntity> relations = agentTagRelationMapper.findByAgentId(agent.getId());
                    if (relations.isEmpty()) {
                        agent.setTags(Collections.emptyList());
                    } else {
                        List<AgentTagEntity> tags = new ArrayList<>(relations.size());
                        for (AgentTagRelationEntity relation : relations) {
                            AgentTagEntity tag = tagsMap.get(relation.getTagId());
                            if (tag != null) {
                                tags.add(tag);
                            }
                        }
                        agent.setTags(tags);
                    }
                }
            }
        } catch (Exception e) {
            log.error("同步填充标签信息失败: {}", e.getMessage());
            list.forEach(agent -> {
                if (agent != null) {
                    agent.setTags(Collections.emptyList());
                }
            });
        }
    }

    /**
     * 设置默认社交信息
     */
    private void setDefaultSocialInfo(List<AgentEntity> list) {
        for (AgentEntity agent : list) {
            if (agent != null) {
                if (agent.getLikeCount() == null) agent.setLikeCount(0);
                if (agent.getCommentCount() == null) agent.setCommentCount(0);
                if (agent.getFavoriteCount() == null) agent.setFavoriteCount(0);
                if (agent.getIsLiked() == null) agent.setIsLiked(0);
            }
        }
    }

    /**
     * 批量获取智能体标签
     */
    private Map<Long, List<AgentTagEntity>> fetchAgentTagsInBatch(List<Long> agentIds) {
        if (CollectionUtils.isEmpty(agentIds)) {
            return Collections.emptyMap();
        }

        // 批量获取所有标签关系
        List<AgentTagRelationEntity> allTagRelations = agentTagRelationMapper.findByAgentIds(agentIds);
        if (CollectionUtils.isEmpty(allTagRelations)) {
            return Collections.emptyMap();
        }

        // 获取所有标签
        Map<Long, AgentTagEntity> tagsMap = agentTagService.findTagsMap();

        // 按智能体ID分组
        Map<Long, List<AgentTagEntity>> result = new HashMap<>();
        Map<Long, List<AgentTagRelationEntity>> relationMap = allTagRelations.stream()
                .collect(Collectors.groupingBy(AgentTagRelationEntity::getAgentId));

        relationMap.forEach((agentId, relations) -> {
            List<AgentTagEntity> tags = relations.stream()
                    .map(relation -> tagsMap.get(relation.getTagId()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            result.put(agentId, tags);
        });

        return result;
    }

    @Override
    public List<AgentEntity> findByIdsAndUsername(List<Long> ids, String username) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<AgentEntity> agentList = queryByIdsKeepOrder(ids);
        if (!CollectionUtils.isEmpty(agentList)) {
            // 填充标签信息
            fillAgentListTags(agentList);
            // 填充点赞和评论信息
            fillAgentsWithSocialInfo(agentList, username);
        }

        return agentList;
    }

    @Override
    public void updateBgUrl(Long agentId, String bgUrl) {
        if (agentId == null || !StringUtils.hasText(bgUrl)) {
            return;
        }
        this.baseMapper.updateBgUrlAndThumbBgUrlNull(agentId, bgUrl);
    }

    @Override
    public void updateAvatarUrl(Long agentId, String avatarUrl) {
        if (agentId == null || !StringUtils.hasText(avatarUrl)) {
            return;
        }
        this.baseMapper.updateAvatarUrl(agentId, avatarUrl);
    }

    /**
     * 删除智能体标签关系
     * 优化: 添加批量删除方法,批量处理避免多次数据库调用
     */
    private void deleteAgentTagRelations(Long agentId) {
        if (agentId == null) {
            return;
        }

        try {
            // 直接调用mapper执行删除操作
            agentTagRelationMapper.deleteByAgentId(agentId);
        } catch (Exception e) {
            log.error("删除智能体标签关系失败: {}", e.getMessage());
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 保存智能体标签关系
     * 优化: 批量插入处理，减少数据库交互次数
     */
    private void saveAgentTagRelations(AgentEntity entity) {
        if (entity == null || entity.getId() == null || CollectionUtils.isEmpty(entity.getTags())) {
            return;
        }

        List<AgentTagRelationEntity> relations = entity.getTags().stream()
                .map(tag -> {
                    AgentTagRelationEntity relation = new AgentTagRelationEntity();
                    relation.setAgentId(entity.getId());
                    relation.setTagId(tag.getId());
                    return relation;
                })
                .toList();

        try {
            // 批量插入替代单条插入
            if (!relations.isEmpty()) {
                agentTagRelationMapper.insertBatch(relations);
            }
        } catch (Exception e) {
            log.error("保存智能体标签关系失败: {}", e.getMessage());
            // 单个插入作为降级处理
            relations.forEach(relation -> {
                try {
                    agentTagRelationMapper.insert(relation);
                } catch (Exception ex) {
                    log.error("单个保存标签关系失败: {}", ex.getMessage());
                }
            });
        }
    }

    /**
     * 为单个智能体填充标签信息
     */
    private void fillEntityTag(AgentEntity entity, Map<Long, AgentTagEntity> tagsMap,
                               List<AgentTagRelationEntity> tagRelations) {
        if (entity == null) {
            return;
        }

        if (CollectionUtils.isEmpty(tagRelations)) {
            entity.setTags(new ArrayList<>());
            return;
        }

        List<AgentTagEntity> tags = new ArrayList<>();
        for (AgentTagRelationEntity relation : tagRelations) {
            AgentTagEntity tag = tagsMap.get(relation.getTagId());
            if (tag != null) {
                tags.add(tag);
            }
        }
        entity.setTags(tags);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void updateThumbnailNotExist(AgentEntity entity) {
        if (entity == null || !StringUtils.hasText(entity.getBgUrl())) {
            return;
        }
        if (entity.getBgThumbnailUrl() != null && !entity.getBgThumbnailUrl().isEmpty()) {
            return;
        }


        if (!UploadFileUtil.checkFileExistence(entity.getBgUrl())) {
            log.error("Background image file not found: {}", entity.getBgUrl());
            return;
        }

        long startTime = System.currentTimeMillis();
        try {
            log.info("开始生成缩略图, 原图URL: {}", entity.getBgUrl());

            // 生成缩略图文件名，添加时间戳避免冲突
            String originalPath = entity.getBgUrl();
            int lastDotIndex = originalPath.lastIndexOf(".");
            String basePath = lastDotIndex != -1 ? originalPath.substring(0, lastDotIndex) : originalPath;
            String timestamp = String.valueOf(System.currentTimeMillis());
            String outputFilePath = basePath + "_" + timestamp + "_thumb.jpg";

            log.info("生成缩略图路径: {}", outputFilePath);

            // 使用配置的参数创建并保存缩略图
            outputFilePath = ImageUtils.createThumbnailAndSaveToFile(
                    entity.getBgUrl(),
                    thumbnailWidth,
                    thumbnailHeight,
                    "jpg",
                    thumbnailQuality,
                    outputFilePath
            );

            // 更新实体和数据库
            entity.setBgThumbnailUrl(outputFilePath);
            this.baseMapper.updateBgThumbnailUrl(entity.getId(), entity.getBgThumbnailUrl());

            // 更新缓存
            updateAgentCache(entity);

            // 清除相关分页缓存
            clearAgentPageCache(entity.getId());

            log.info("缩略图生成成功: {}, 耗时: {}ms", outputFilePath, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            entity.setBgThumbnailUrl(entity.getBgUrl());
            this.baseMapper.updateBgThumbnailUrl(entity.getId(), entity.getBgThumbnailUrl());

            // 更新缓存
            updateAgentCache(entity);

            // 清除相关分页缓存
            clearAgentPageCache(entity.getId());

            log.info("缩略图生成失败，用原图路径: {}, 耗时: {}ms", entity.getBgUrl(), System.currentTimeMillis() - startTime);
            log.error("创建缩略图失败: {}", e.getMessage(), e);
            // 事务会自动回滚
        }
    }

    /**
     * 为智能体列表填充标签信息（优化版）
     */
    private void fillAgentListTags(List<AgentEntity> agentList) {
        if (CollectionUtils.isEmpty(agentList)) {
            return;
        }

        // 如果列表过大，分批处理以减轻内存压力
        if (agentList.size() > 500) {
            int batchSize = 200;
            for (int i = 0; i < agentList.size(); i += batchSize) {
                int end = Math.min(i + batchSize, agentList.size());
                fillAgentListTagsBatch(agentList.subList(i, end));
            }
            return;
        }

        fillAgentListTagsBatch(agentList);
    }

    /**
     * 批量处理智能体标签填充
     */
    private void fillAgentListTagsBatch(List<AgentEntity> agentBatch) {
        if (CollectionUtils.isEmpty(agentBatch)) {
            return;
        }

        // 获取所有智能体ID
        List<Long> agentIds = agentBatch.stream()
                .map(AgentEntity::getId)
                .collect(Collectors.toList());

        try {
            // 使用CompletableFuture并行获取数据
            CompletableFuture<List<AgentTagRelationEntity>> tagRelationsFuture =
                    CompletableFuture.supplyAsync(() -> agentTagRelationMapper.findByAgentIds(agentIds))
                            .orTimeout(2, TimeUnit.SECONDS)
                            .exceptionally(ex -> {
                                log.warn("获取标签关系超时: {}", ex.getMessage());
                                return Collections.emptyList();
                            });

            CompletableFuture<Map<Long, AgentTypeEntity>> typeMapFuture =
                    CompletableFuture.supplyAsync(agentTypeService::findTypesMap)
                            .orTimeout(2, TimeUnit.SECONDS)
                            .exceptionally(ex -> {
                                log.warn("获取类型映射超时: {}", ex.getMessage());
                                return Collections.emptyMap();
                            });

            CompletableFuture<Map<Long, AgentTagEntity>> tagsMapFuture =
                    CompletableFuture.supplyAsync(agentTagService::findTagsMap)
                            .orTimeout(2, TimeUnit.SECONDS)
                            .exceptionally(ex -> {
                                log.warn("获取标签映射超时: {}", ex.getMessage());
                                return Collections.emptyMap();
                            });

            // 等待所有数据获取完成（超时保护）
            try {
                CompletableFuture.allOf(tagRelationsFuture, typeMapFuture, tagsMapFuture)
                        .get(2, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.warn("并行获取标签数据超时: {}", e.getMessage());
            }

            List<AgentTagRelationEntity> allTagRelations = tagRelationsFuture.getNow(Collections.emptyList());
            Map<Long, AgentTypeEntity> typeMap = typeMapFuture.getNow(Collections.emptyMap());
            Map<Long, AgentTagEntity> tagsMap = tagsMapFuture.getNow(Collections.emptyMap());

            if (CollectionUtils.isEmpty(allTagRelations)) {
                agentBatch.forEach(agent -> agent.setTags(new ArrayList<>()));
                return;
            }

            // 预处理：按智能体ID分组标签关系（用HashMap提高后续查找性能）
            Map<Long, List<AgentTagRelationEntity>> relationMap = new HashMap<>(agentBatch.size());
            for (AgentTagRelationEntity relation : allTagRelations) {
                relationMap.computeIfAbsent(relation.getAgentId(), k -> new ArrayList<>()).add(relation);
            }

            // 使用并行流加速处理
            agentBatch.parallelStream().forEach(agent -> {
                List<AgentTagRelationEntity> relations = relationMap.getOrDefault(agent.getId(), Collections.emptyList());
                fillEntityTag(agent, tagsMap, relations);

                if (agent.getTypeId() != null) {
                    agent.setType(typeMap.get(agent.getTypeId()));
                }
            });
        } catch (Exception e) {
            log.error("填充智能体标签信息失败: {}", e.getMessage());
            // 确保至少有空标签列表
            agentBatch.forEach(agent -> {
                if (agent.getTags() == null) {
                    agent.setTags(new ArrayList<>());
                }
            });
        }
    }

    @Override
    public void updatePopularity(Long agentId, Integer increment) {
        if (agentId == null || increment == null) {
            return;
        }

        try {
            log.info("更新智能体[{}]热度值，增加[{}]点", agentId, increment);

        } catch (Exception e) {
            log.error("更新智能体[{}]热度值失败: {} {}", agentId, e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordAgentUsage(Long agentId, String username) {
        // 参数验证
        if (agentId == null || !StringUtils.hasText(username)) {
            return false;
        }

        // 获取智能体
        Optional<AgentEntity> agentOptional = this.baseMapper.findById(agentId);
        if (agentOptional.isEmpty()) {
            log.error("记录智能体[{}]使用失败，智能体不存在", agentId);
            return false;
        }

        AgentEntity entity = agentOptional.get();

        // 记录使用情况并判断是否首次使用
        boolean isFirstUse = agentUsageRecordService.recordUsage(agentId, username);

        // 记录每日使用情况
        agentDailyUsageRecordService.recordDailyUsage(agentId, username);

        // 首次使用时增加热度值并发送通知
        if (isFirstUse) {
            updatePopularity(agentId, (int) (Math.random() * 41 + 10));

            // 如果是别人创建的智能体，发送通知
            String creator = entity.getCreator();
            if (!creator.equals(username)) {
                try {
                    notifyProxyService.insertInteractiveMessage(
                            new InteractiveNotifySubmitDTO(
                                    "{nickname}使用了我创作的" + "\"" + entity.getName() + "\"",
                                    NotifyConstant.INTERACTIVE_AGENT_USE,
                                    agentId,
                                    creator,
                                    username
                            )
                    );
                } catch (Exception e) {
                    log.warn("发送智能体使用通知失败: {}", e.getMessage());
                }
            }

            log.info("用户[{}]首次使用智能体[{}]，热度值+1", username, agentId);
        }

        return true;
    }

    @Override
    @CacheEvict(cacheNames = "agentSetting", key = "'set:'+#entity.username +':'+ #entity.agentId")
    public void saveSetting(MyAgentCommonSettingEntity entity) {
        if (entity == null || entity.getUsername() == null) {
            throw new BusinessException("用户名不能为空");
        }

        if (entity.getId() == null) {
            settingMapper.insert(entity);
        } else {
            settingMapper.updateById(entity);
        }
        log.info("保存智能体设置成功: {}", entity);
    }

    @Override
    public MyAgentCommonSettingEntity findSettingById(Long id) {
        if (id == null) {
            return null;
        }
        return settingMapper.selectById(id);
    }

    @Override
    public MyAgentCommonSettingEntity findSettingByUsername(String username) {
        return settingMapper.findByUsername(username).orElse(null);
    }

    @Override
    @Cacheable(cacheNames = "agentSetting", key = "'set:'+#username +':'+ #agentId")
    public MyAgentCommonSettingEntity findSettingByAgentIdAndUsername(Long agentId, String username) {
        return settingMapper.findByAgentIdAndUsername(agentId, username).orElse(null);
    }

    /**
     * 填充智能体的点赞信息
     *
     * @param entity   智能体实体
     * @param username 用户名
     */
    private void fillAgentWithIsLikeInfo(AgentEntity entity, String username) {
        if (entity == null) {
            return;
        }

        // 设置当前用户是否点赞
        if (StringUtils.hasText(username)) {
            entity.setIsLiked(agentLikeService.isLiked(entity.getId(), username));
        } else {
            entity.setIsLiked(0);
        }
    }

    /**
     * 填充智能体列表的社交信息（点赞、评论等）
     * 优化: 使用高效的并行处理方式
     *
     * @param agentList 智能体列表
     * @param username  用户名
     */
    private void fillAgentsWithSocialInfo(List<AgentEntity> agentList, String username) {
        // 直接使用高效的实现
        fillAgentsWithSocialInfoEfficiently(agentList, username);
    }

    /**
     * 分页查询用户点赞的智能体
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param query    查询条件
     * @return 分页结果
     */
    @Override
    public PageInfo<AgentEntity> pageLikedByUsername(int pageNum, int pageSize, MyLikeAgentQueryDTO query) {
        PageHelper.startPage(pageNum, pageSize);
        List<AgentEntity> list = this.baseMapper.pageLikedByUsername(query);

        if (!CollectionUtils.isEmpty(list)) {
            // 填充智能体标签
            fillAgentListTags(list);

            // 填充社交信息（点赞数、评论数、收藏数等）
            fillAgentsWithSocialInfo(list, query.getUsername());
        }

        return new PageInfo<>(list);
    }

    /**
     * 分页查询用户收藏的智能体
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param query    查询条件
     * @return 分页结果
     */
    @Override
    public PageInfo<AgentEntity> pageFavoriteByUsername(int pageNum, int pageSize, MyFavoriteAgentQueryDTO query) {
        PageHelper.startPage(pageNum, pageSize);
        List<AgentEntity> list = this.baseMapper.pageFavoriteByUsername(query);

        if (!CollectionUtils.isEmpty(list)) {
            // 填充智能体标签
            fillAgentListTags(list);

            // 填充社交信息（点赞数、评论数、收藏数等）
            fillAgentsWithSocialInfo(list, query.getUsername());
        }

        return new PageInfo<>(list);
    }

    @Override
    public PageInfo<AgentEntity> pageCommentByUsername(int pageNum, int pageSize, MyCommentAgentQueryDTO query) {
        PageHelper.startPage(pageNum, pageSize);
        List<AgentEntity> list = this.baseMapper.pageCommentByUsername(query);
        if (!CollectionUtils.isEmpty(list)) {
            // 填充智能体标签
            fillAgentListTags(list);

            // 填充社交信息（点赞数、评论数、收藏数等）
            fillAgentsWithSocialInfo(list, query.getUsername());
        }
        return new PageInfo<>(list);
    }

    /**
     * 记录智能体ID与缓存键的关联
     * 在缓存结果时调用此方法记录关联关系
     */


    /**
     * 记录多个智能体ID与缓存键的关联
     */


    /**
     * 清除与特定智能体相关的所有分页缓存
     * 当智能体属性变更时调用(如点赞、评论、收藏等)
     *
     * @param agentId 智能体ID
     */
    public void clearAgentPageCache(Long agentId) {
        if (agentId == null) {
            return;
        }

        long startTime = System.currentTimeMillis();
        log.info("开始清除智能体[{}]相关的分页缓存", agentId);

        try {
            // 获取缓存对象
            Cache pageCache = cacheManager.getCache(AgentCacheConstant.AGENT_PAGE_CACHE);
            if (pageCache == null) {
                return;

                // 获取与该智能体相关的所有缓存键

                // 如果没有记录相关缓存键，则清除整个缓存（兜底策略）
                // 只清除与该智能体相关的缓存

                // 清除已处理的缓存键记录
            }

            log.info("清除智能体[{}]相关分页缓存完成，耗时: {}ms", agentId, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.warn("清除智能体[{}]相关分页缓存失败: {}", agentId, e.getMessage());
        }
    }

    /**
     * 清除与特定用户相关的所有缓存
     * 当用户执行交互操作时调用(如点赞、评论、收藏等)
     *
     * @param username 用户名
     */
    public void clearUserRelatedCache(String username) {
        if (!StringUtils.hasText(username)) {
            return;
        }

        long startTime = System.currentTimeMillis();
        log.info("开始清除用户[{}]相关的缓存", username);

        try {
            // 获取缓存对象
            Cache pageCache = cacheManager.getCache(AgentCacheConstant.AGENT_PAGE_CACHE);
            if (pageCache != null) {
                // 清除可能包含用户交互状态的缓存
                pageCache.clear();
            }

            // 清除用户创建的智能体计数缓存
            Cache cntCache = cacheManager.getCache(CacheCntValue);
            if (cntCache != null) {
                cntCache.evict("ac:" + username);
            }

            log.info("清除用户[{}]相关缓存完成，耗时: {}ms", username, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.warn("清除用户[{}]相关缓存失败: {}", username, e.getMessage());
        }
    }

    /**
     * 清除所有分页缓存
     * 在批量操作或影响多条记录的变更后调用
     */
    public void clearAllPageCache() {
        long startTime = System.currentTimeMillis();
        log.info("开始清除所有智能体分页缓存");

        try {
            Cache pageCache = cacheManager.getCache(AgentCacheConstant.AGENT_PAGE_CACHE);
            if (pageCache != null) {
                pageCache.clear();
            }

            log.info("清除所有智能体分页缓存完成，耗时: {}ms", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.warn("清除所有智能体分页缓存失败: {}", e.getMessage());
        }
    }

    @Override
    public int countTodayNewAgents(LocalDateTime startTime, LocalDateTime endTime) {
        return this.baseMapper.countNewAgentsByTimeRange(startTime, endTime);
    }

    @Override
    public int countTotalAgents() {
        return this.baseMapper.countTotalAgents();
    }

    @Override
    public List<AgentTypeStatisticsVO> countAgentsByType() {
        // 直接调用Mapper的方法，使用SQL的GROUP BY进行统计
        return baseMapper.countAgentsByType();
    }

    @Override
    public List<AgentStatusEntity> findAllStatusByAgentIds(List<Long> agentIds) {
        return this.baseMapper.findAllStatusByAgentIds(agentIds);
    }

    @Override
    public List<Long> findAgentIdsByNameLike(String agentName) {
        if (agentName == null || agentName.trim().isEmpty()) {
            return Collections.emptyList();
        }
        return this.baseMapper.findAgentIdsByNameLike(agentName.trim());
    }

    private static class SocialInfoData {
        final Map<Long, AgentStatisticsEntity> statisticsEntityMap;
        final Map<Long, Integer> userLikesMap;

        SocialInfoData() {
            this.statisticsEntityMap = Collections.emptyMap();
            this.userLikesMap = Collections.emptyMap();
        }

        SocialInfoData(Map<Long, AgentStatisticsEntity> statisticsEntityMap, Map<Long, Integer> userLikesMap) {
            this.statisticsEntityMap = statisticsEntityMap != null ? statisticsEntityMap : Collections.emptyMap();
            this.userLikesMap = userLikesMap != null ? userLikesMap : Collections.emptyMap();
        }
    }
}
