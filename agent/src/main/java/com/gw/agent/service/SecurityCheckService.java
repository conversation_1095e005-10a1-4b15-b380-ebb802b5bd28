package com.gw.agent.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.security.WxMaMediaSecCheckCheckRequest;
import cn.binarywang.wx.miniapp.bean.security.WxMaMsgSecCheckCheckRequest;
import cn.binarywang.wx.miniapp.bean.security.WxMaMsgSecCheckCheckResponse;
import com.gw.agent.constant.ImageCheckConstant;
import com.gw.agent.entity.AgentEntity;
import com.gw.agent.entity.ImageCheckProcessEntity;
import com.gw.agent.event.SecurityCheckPassedEvent;
import com.gw.agent.exception.SecurityCheckException;
import com.gw.agent.mapper.sql.AgentMapper;
import com.gw.agent.mapper.sql.ImageCheckProcessMapper;
import com.gw.common.agent.constant.AgentConstant;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.ciModel.auditing.ImageAuditingRequest;
import com.qcloud.cos.model.ciModel.auditing.ImageAuditingResponse;
import com.qcloud.cos.model.ciModel.auditing.TextAuditingRequest;
import com.qcloud.cos.model.ciModel.auditing.TextAuditingResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.util.Base64;
import java.util.List;

/**
 * 安全检查服务，负责对内容进行异步安全检查
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class SecurityCheckService {

    private final WxMaService wxMaService;
    private final ApplicationEventPublisher eventPublisher;
    private final AgentMapper agentMapper;
    private final ImageCheckProcessMapper imageCheckProcessMapper;
    private final COSClient cosClient;
    private final SecurityCheckErrorMessageBuilder errorMessageBuilder;
    private final SensitiveWordDetectionService sensitiveWordDetectionService;
    @Value("${host.url:https://www.guanwei-ai.com}")
    private String hostUrl;

    @Value("${tencent.cos.bucket-name}")
    private String bucketName;

    // 安全检查平台类型
    private static final String PLATFORM_WECHAT = "WECHAT";
    private static final String PLATFORM_TENCENT_CLOUD = "TENCENT_CLOUD";

    /**
     * 异步执行安全检查
     * 使用配置的taskExecutor线程池
     *
     * @param entity 需要检查的智能体实体
     */
    @Async("taskExecutor")
    public void performSecurityCheck(AgentEntity entity) {
        Long agentId = entity.getId();
        String agentInfo = String.format("智能体[ID:%d]", agentId);

        try {
            log.info("{}开始安全检查，微信OpenID: {}", agentInfo, entity.getWxOpenId());

            // 检查名称安全性
            checkTextWithTencentCOS("角色名称", entity.getName(), agentId);

            // 检查简介安全性
            checkTextWithTencentCOS("角色简介", entity.getIntroduction(), agentId);

            // 检查角色设定
            checkTextWithTencentCOS("角色设定", entity.getIdentity(), agentId);

            // 检查Profile相关内容
            if (entity.getProfile() != null) {
                var profile = entity.getProfile();

                if (profile.getBackground() != null) {
                    checkTextWithTencentCOS("背景", profile.getBackground(), agentId);
                }
                if (profile.getPrologue() != null) {
                    checkTextWithTencentCOS("开场白", profile.getPrologue(), agentId);
                }
            }

            // 检查形象图片
            if (entity.getBgUrl() != null) {
                checkImageWithTencentCOS("形象图片", entity.getBgUrl(), agentId, agentId.toString());
            }

            // 所有检查通过
            handleSecurityCheckSuccess(agentId);
            log.info("{}全部内容安全检查通过", agentInfo);

        } catch (SecurityCheckException e) {
            log.error("{}内容安全检查失败，错误: {}", agentInfo, e.getMessage(), e);
            handleSecurityCheckFailure(agentId, e);
        } catch (Exception e) {
            // 处理未预期的异常
            log.error("{}内容安全检查失败 处理未预期的异常，错误: {}", agentInfo, e.getMessage(), e);
            SecurityCheckException securityException = new SecurityCheckException.SecurityCheckBusinessException(
                    "系统检查",
                    errorMessageBuilder.buildSystemErrorMessage("安全检查", "执行", e),
                    agentId,
                    e
            );
            handleSecurityCheckFailure(agentId, securityException);
        }
    }

    public void checkImage(String type, String path, String openId, Long agentId) throws SecurityCheckException {
        String url = hostUrl + "/" + path;
        log.info("开始微信{}图片检查，智能体ID: {}，图片路径: {}", type, agentId, url);

        try {
            WxMaMediaSecCheckCheckRequest req = WxMaMediaSecCheckCheckRequest.builder()
                    .mediaUrl(url)
                    .mediaType(2)
                    .openid(openId)
                    .scene(1)
                    .version(2)
                    .build();
            var response = wxMaService.getSecurityService().mediaCheckAsync(req);

            if (response != null && response.getTraceId() != null) {
                imageCheckProcessMapper.insert(ImageCheckProcessEntity.builder()
                        .checkId(agentId.toString())
                        .checkStatus(ImageCheckConstant.CHECK_PROCESS_STATUS)
                        .traceId(response.getTraceId())
                        .imagePath(path)
                        .build());
                log.info("微信{}图片检查提交成功，智能体ID: {}，traceId: {}", type, agentId, response.getTraceId());
            } else {
                log.error("微信{}图片检查异常，智能体ID: {}，响应: {}", type, agentId, response);
                throw new SecurityCheckException.WechatSecurityCheckException(
                        type, "图片检查提交失败，请稍后重试", agentId);
            }
        } catch (SecurityCheckException e) {
            throw e;
        } catch (Exception e) {
            log.error("微信{}图片检查系统异常，智能体ID: {}，错误: {}", type, agentId, e.getMessage(), e);
            String errorMessage = errorMessageBuilder.buildSystemErrorMessage(type, "图片检查", e);
            throw new SecurityCheckException.WechatSecurityCheckException(type, errorMessage, agentId, e);
        }
    }

    private void checkImage(String type, File file, AgentEntity entity) throws SecurityCheckException {
        if (file == null || !file.exists()) {
            log.debug("{}图片文件不存在，跳过检查，智能体ID: {}", type, entity.getId());
            return;
        }

        Long agentId = entity.getId();
        log.info("开始微信{}图片检查，智能体ID: {}，文件路径: {}", type, agentId, file.getPath());

        try {
            boolean checkResult = wxMaService.getSecurityService().checkImage(file);

            if (!checkResult) {
                String errorMsg = errorMessageBuilder.buildUserFriendlyMessage(type + "图片");
                log.warn("微信{}图片检查不通过，智能体ID: {}", type, agentId);
                throw new SecurityCheckException.WechatSecurityCheckException(type, errorMsg, agentId);
            }

            log.info("完成微信{}图片检查，智能体ID: {}", type, agentId);

        } catch (SecurityCheckException e) {
            throw e;
        } catch (Exception e) {
            log.error("微信{}图片检查系统异常，智能体ID: {}，错误: {}", type, agentId, e.getMessage(), e);
            String errorMessage = errorMessageBuilder.buildSystemErrorMessage(type, "图片检查", e);
            throw new SecurityCheckException.WechatSecurityCheckException(type, errorMessage, agentId, e);
        }
    }

    private void checkContent(String type, String content, AgentEntity entity) throws SecurityCheckException {
        if (!StringUtils.hasText(content)) {
            log.debug("{}文本内容为空，跳过检查，智能体ID: {}", type, entity.getId());
            return;
        }

        Long agentId = entity.getId();
        log.info("开始微信{}文本检查，智能体ID: {}，文本长度: {}", type, agentId, content.length());

        try {
            WxMaMsgSecCheckCheckResponse response;

            if (entity.getWxOpenId() != null) {
                response = wxMaService.getSecurityService()
                        .checkMessage(WxMaMsgSecCheckCheckRequest.builder()
                                .content(content)
                                .openid(entity.getWxOpenId())
                                .build());
            } else {
                response = wxMaService.getSecurityService()
                        .checkMessage(WxMaMsgSecCheckCheckRequest.builder()
                                .content(content)
                                .build());
            }

            if (!response.getErrcode().equals(0)) {
                String errorMsg = errorMessageBuilder.buildWechatErrorMessage(type, response);
                log.warn("微信{}文本检查不通过，智能体ID: {}，结果: {}，错误详情: {}",
                        type, agentId, response.getErrcode(), response.getDetail());
                throw new SecurityCheckException.WechatSecurityCheckException(type, errorMsg, agentId);
            }

            log.info("完成微信{}文本检查，智能体ID: {}", type, agentId);

        } catch (SecurityCheckException e) {
            throw e;
        } catch (Exception e) {
            log.error("微信{}文本检查系统异常，智能体ID: {}，错误: {}", type, agentId, e.getMessage(), e);
            String errorMessage = errorMessageBuilder.buildSystemErrorMessage(type, "文本检查", e);
            throw new SecurityCheckException.WechatSecurityCheckException(type, errorMessage, agentId, e);
        }
    }



    /**
     * 处理安全检查成功
     */
    private void handleSecurityCheckSuccess(Long agentId) {
        try {
            agentMapper.updateSecurityCheckResultById(agentId, "审核通过");
            eventPublisher.publishEvent(new SecurityCheckPassedEvent(agentMapper.findById(agentId).get(), false));
            log.info("智能体[ID:{}]安全检查状态更新成功", agentId);
        } catch (Exception e) {
            log.error("智能体[ID:{}]安全检查成功后状态更新失败: {}", agentId, e.getMessage(), e);
        }
    }

    /**
     * 处理安全检查失败
     */
    private void handleSecurityCheckFailure(Long agentId, SecurityCheckException e) {
        String userFriendlyMessage = e.getUserFriendlyMessage();
        String adminMessage = errorMessageBuilder.buildAdminErrorMessage(
                e.getCheckType(),
                e.getPlatform(),
                e.getErrorCode(),
                e.getMessage(),
                agentId
        );

        try {
            // 更新智能体状态为审核失败
            agentMapper.updateStatus(agentId, AgentConstant.AGENT_CHECK_STATUS_FAIL);
            // 保存用户友好的错误信息
            agentMapper.updateSecurityCheckResultById(agentId, userFriendlyMessage);

            // 记录详细的管理员日志
            log.error("智能体[ID:{}]安全检查失败: {}", agentId, adminMessage, e);
            eventPublisher.publishEvent(new SecurityCheckPassedEvent(agentMapper.findById(agentId).get(), true));
        } catch (Exception dbException) {
            log.error("智能体[ID:{}]安全检查失败后状态更新异常: {}", agentId, dbException.getMessage(), dbException);
        }
    }

    /**
     * @deprecated 使用 handleSecurityCheckFailure 替代
     */
    @Deprecated
    private void updateAgentFailStatus(Long id, String errorMsg) {
        agentMapper.updateStatus(id, AgentConstant.AGENT_CHECK_STATUS_FAIL);
        agentMapper.updateSecurityCheckResultById(id, errorMsg);
    }

    /**
     * 使用腾讯云COS进行图片审核（通用方法）
     *
     * @param type 检查类型描述
     * @param path 图片URL地址
     * @throws SecurityCheckException 审核异常
     */
    public void checkImageWithTencentCOS(String type, String path) throws SecurityCheckException {
        checkImageWithTencentCOS(type, path, null, null);
    }

    /**
     * 使用腾讯云COS进行图片审核（带业务ID）
     *
     * @param type       检查类型描述
     * @param path       图片URL地址
     * @param businessId 业务ID（可选，用于标识和日志记录）
     * @param dataId     数据标识（可选，用于审核请求标识）
     * @throws SecurityCheckException 审核异常
     */
    public void checkImageWithTencentCOS(String type, String path, Long businessId, String dataId) throws SecurityCheckException {
        if (!StringUtils.hasText(path)) {
            if (businessId != null) {
                log.debug("{}图片路径为空，跳过检查，业务ID: {}", type, businessId);
            } else {
                log.debug("{}图片路径为空，跳过检查", type);
            }
            return;
        }

        String imageUrl = hostUrl + "/" + path;
        String logInfo = businessId != null ? String.format("业务ID: %d", businessId) : "通用检查";
        log.info("开始腾讯云{}图片审核，{}，图片URL: {}", type, logInfo, imageUrl);

        try {
            // 创建图片审核请求对象
            ImageAuditingRequest request = new ImageAuditingRequest();
            request.setBucketName(bucketName);
            request.setDetectUrl(imageUrl);

            // 设置审核参数
            request.setAsync("0"); // 同步审核
            request.setLargeImageDetect("1"); // 处理大图片

            // 设置数据标识
            if (StringUtils.hasText(dataId)) {
                request.setDataId(dataId);
            } else if (businessId != null) {
                request.setDataId(businessId.toString());
            }

            // 调用腾讯云图片审核接口
            ImageAuditingResponse response = cosClient.imageAuditing(request);

            // 处理审核结果
            handleTencentImageAuditResult(type, response, businessId);

            log.info("完成腾讯云{}图片审核，{}，审核结果: {}",
                    type, logInfo, response.getResult());

        } catch (SecurityCheckException e) {
            throw e; // 重新抛出安全检查异常
        } catch (Exception e) {
            log.error("腾讯云{}图片审核系统异常，{}，错误: {}", type, logInfo, e.getMessage(), e);
            String errorMessage = errorMessageBuilder.buildSystemErrorMessage(type, "图片审核", e);
            throw new SecurityCheckException.TencentCloudSecurityCheckException(type, errorMessage, businessId, e);
        }
    }

    /**
     * @deprecated 使用 checkImageWithTencentCOS(String, String) 替代
     */
    @Deprecated
    public void checkImageWithTencentCOS(String type, String path, Long agentId) throws SecurityCheckException {
        checkImageWithTencentCOS(type, path, agentId, null);
    }

    /**
     * 处理腾讯云图片审核结果
     *
     * @param type       检查类型
     * @param response   审核响应
     * @param businessId 业务ID（可选）
     * @throws SecurityCheckException 审核不通过时抛出异常
     */
    private void handleTencentImageAuditResult(String type, ImageAuditingResponse response, Long businessId)
            throws SecurityCheckException {
        String logInfo = businessId != null ? String.format("业务ID: %d", businessId) : "通用检查";
        if (response == null) {
            log.error("腾讯云{}图片审核响应为空，{}", type, logInfo);
            throw new SecurityCheckException.TencentCloudSecurityCheckException(
                    type, "审核服务响应异常，请稍后重试", businessId);
        }

        // 获取审核结果
        String result = response.getResult();
        log.info("腾讯云{}图片审核结果，{}，result: {}", type, logInfo, result);

        // result: 0-正常, 1-违规, 2-疑似
        if ("1".equals(result)) {
            // 违规内容，构建详细错误信息
            String errorMsg = errorMessageBuilder.buildTencentImageErrorMessage(type, response);
            log.warn("腾讯云{}图片审核不通过，{}，原因: {}", type, logInfo, errorMsg);
            throw new SecurityCheckException.TencentCloudSecurityCheckException(type, errorMsg, businessId);

        } else if ("2".equals(result)) {
            // 疑似违规，记录但不拦截（可根据业务需求调整）
            String suspiciousMsg = errorMessageBuilder.buildTencentImageErrorMessage(type, response);
            log.warn("腾讯云{}图片审核疑似违规，{}，原因: {}", type, logInfo, suspiciousMsg);

        }
    }

    /**
     * @deprecated 使用带businessId参数的方法替代
     */
    @Deprecated
    private void handleTencentImageAuditResult(String type, ImageAuditingResponse response) throws Exception {
        handleTencentImageAuditResult(type, response, null);
    }



    /**
     * 使用腾讯云COS进行图片批量审核
     *
     * @param type      检查类型描述
     * @param imageUrls 图片URL列表
     * @param agentId   智能体ID
     * @throws SecurityCheckException 审核异常
     */
    public void batchCheckImagesWithTencentCOS(String type, List<String> imageUrls, Long agentId)
            throws SecurityCheckException {
        if (imageUrls == null || imageUrls.isEmpty()) {
            log.debug("{}图片URL列表为空，跳过批量检查，智能体ID: {}", type, agentId);
            return;
        }

        log.info("开始腾讯云{}图片批量审核，智能体ID: {}，图片数量: {}", type, agentId, imageUrls.size());

        for (int i = 0; i < imageUrls.size(); i++) {
            String imageUrl = imageUrls.get(i);
            try {
                checkImageWithTencentCOS(type + "(第" + (i + 1) + "张)", imageUrl, agentId, agentId != null ? agentId.toString() : null);
            } catch (SecurityCheckException e) {
                String batchErrorMsg = errorMessageBuilder.buildBatchCheckErrorMessage(
                        type + "图片", imageUrls.size(), i, e.getMessage());
                log.error("批量图片审核失败，智能体ID: {}，第{}张图片: {}，错误: {}",
                        agentId, i + 1, imageUrl, e.getMessage());
                throw new SecurityCheckException.TencentCloudSecurityCheckException(
                        type + "批量图片", batchErrorMsg, agentId, e);
            }
        }

        log.info("完成腾讯云{}图片批量审核，智能体ID: {}，检查图片数量: {}", type, agentId, imageUrls.size());
    }

    /**
     * 使用腾讯云COS进行文本审核（通用方法）
     *
     * @param type    检查类型描述
     * @param content 待审核的文本内容
     * @throws SecurityCheckException 审核异常
     */
    public void checkTextWithTencentCOS(String type, String content) throws SecurityCheckException {
        checkTextWithTencentCOS(type, content, null);
    }

    /**
     * 使用腾讯云COS进行文本审核（带业务ID）
     *
     * @param type       检查类型描述
     * @param content    待审核的文本内容
     * @param businessId 业务ID（可选，用于标识和日志记录）
     * @throws SecurityCheckException 审核异常
     */
    public void checkTextWithTencentCOS(String type, String content, Long businessId) throws SecurityCheckException {
        if (!StringUtils.hasText(content)) {
            if (businessId != null) {
                log.debug("{}文本内容为空，跳过检查，业务ID: {}", type, businessId);
            } else {
                log.debug("{}文本内容为空，跳过检查", type);
            }
            return;
        }

        String logInfo = businessId != null ? String.format("业务ID: %d", businessId) : "通用检查";
        log.info("开始腾讯云{}文本审核，{}，文本: {}", type, logInfo, content);

        try {
            // 创建文本审核请求对象
            TextAuditingRequest request = new TextAuditingRequest();
            request.setBucketName(bucketName);

            // 对文本内容进行Base64编码
            String encodedContent = Base64.getEncoder().encodeToString(content.getBytes("UTF-8"));
            request.getInput().setContent(encodedContent);

            // 调用腾讯云文本审核接口（同步审核）
            TextAuditingResponse response = cosClient.createAuditingTextJobs(request);

            // 处理审核结果
            handleTencentTextAuditResult(type, response, businessId);

            log.info("完成腾讯云{}文本审核，{}，审核结果: {}",
                    type, logInfo, response.getJobsDetail().getResult());

        } catch (SecurityCheckException e) {
            throw e; // 重新抛出安全检查异常
        } catch (Exception e) {
            log.error("腾讯云{}文本审核系统异常，{}，错误: {}", type, logInfo, e.getMessage(), e);
            String errorMessage = errorMessageBuilder.buildSystemErrorMessage(type, "文本审核", e);
            throw new SecurityCheckException.TencentCloudSecurityCheckException(type, errorMessage, businessId, e);
        }
    }

    /**
     * 使用腾讯云COS进行异步文本审核
     *
     * @param type      检查类型描述
     * @param objectKey COS中的文本文件路径
     * @param agentId   智能体ID
     * @return 审核任务ID
     * @throws Exception 审核异常
     */
    public String checkTextFileWithTencentCOS(String type, String objectKey, Long agentId) throws Exception {
        if (!StringUtils.hasText(objectKey)) {
            log.warn("文本文件路径为空，跳过{}检查, agentId: {}", type, agentId);
            return null;
        }

        log.info("开始使用腾讯云COS检查{}文本文件, agentId: {}, 文件路径: {}", type, agentId, objectKey);

        try {
            // 创建文本审核请求对象
            TextAuditingRequest request = new TextAuditingRequest();
            request.setBucketName(bucketName);

            // 设置COS中的文本文件路径
            request.getInput().setObject(objectKey);

            // 设置审核策略（可选）
            // request.getConf().setBizType("your-biz-type");

            // 调用腾讯云文本审核接口（异步审核）
            TextAuditingResponse response = cosClient.createAuditingTextJobs(request);

            String jobId = response.getJobsDetail().getJobId();
            log.info("提交腾讯云{}文本文件审核任务成功, agentId: {}, jobId: {}", type, agentId, jobId);

            return jobId;

        } catch (Exception e) {
            log.error("腾讯云{}文本文件审核失败, agentId: {}, 错误: {}", type, agentId, e.getMessage(), e);
            updateAgentFailStatus(agentId, type + "文本文件审核失败: " + e.getMessage());
            throw new Exception(type + "文本文件安全检查未通过");
        }
    }

    /**
     * 查询文本审核任务结果
     *
     * @param jobId   审核任务ID
     * @param agentId 智能体ID
     * @return 审核结果
     * @throws Exception 查询异常
     */
    public TextAuditingResponse queryTextAuditJob(String jobId, Long agentId) throws Exception {
        if (!StringUtils.hasText(jobId)) {
            throw new Exception("审核任务ID不能为空");
        }

        log.info("查询腾讯云文本审核任务结果, agentId: {}, jobId: {}", agentId, jobId);

        try {
            // 创建查询请求对象
            TextAuditingRequest request = new TextAuditingRequest();
            request.setBucketName(bucketName);
            request.setJobId(jobId);

            // 查询审核任务结果
            TextAuditingResponse response = cosClient.describeAuditingTextJob(request);

            log.info("查询腾讯云文本审核任务结果成功, agentId: {}, jobId: {}, 状态: {}",
                    agentId, jobId, response.getJobsDetail().getState());

            return response;

        } catch (Exception e) {
            log.error("查询腾讯云文本审核任务失败, agentId: {}, jobId: {}, 错误: {}", agentId, jobId, e.getMessage(), e);
            throw new Exception("查询文本审核任务失败: " + e.getMessage());
        }
    }

    /**
     * 处理腾讯云文本审核结果
     *
     * @param type       检查类型
     * @param response   审核响应
     * @param businessId 业务ID（可选）
     * @throws SecurityCheckException 审核不通过时抛出异常
     */
    private void handleTencentTextAuditResult(String type, TextAuditingResponse response, Long businessId)
            throws SecurityCheckException {
        if (response == null || response.getJobsDetail() == null) {
            String logInfo = businessId != null ? String.format("业务ID: %d", businessId) : "通用检查";
            log.error("腾讯云{}文本审核响应为空，{}", type, logInfo);
            throw new SecurityCheckException.TencentCloudSecurityCheckException(
                    type, "审核服务响应异常，请稍后重试", businessId);
        }

        var jobDetail = response.getJobsDetail();
        String result = jobDetail.getResult();
        String logInfo = businessId != null ? String.format("业务ID: %d", businessId) : "通用检查";
        log.info("腾讯云{}文本审核结果，{}，result: {}", type, logInfo, result);

        // result: 0-正常, 1-违规, 2-疑似
        if ("1".equals(result)) {
            // 违规内容，构建详细错误信息
            String errorMsg = errorMessageBuilder.buildTencentTextErrorMessage(type, jobDetail);
            log.warn("腾讯云{}文本审核不通过，{}，原因: {}", type, logInfo, errorMsg);
            throw new SecurityCheckException.TencentCloudSecurityCheckException(type, errorMsg, businessId);

        } else if ("2".equals(result)) {
            // 疑似违规，记录但不拦截（可根据业务需求调整）
            String suspiciousMsg = errorMessageBuilder.buildTencentTextErrorMessage(type, jobDetail);
            log.warn("腾讯云{}文本审核疑似违规，{}，原因: {}", type, logInfo, suspiciousMsg);
            // 根据业务需求，可以选择是否抛出异常
            // throw new SecurityCheckException.TencentCloudSecurityCheckException(type, suspiciousMsg, businessId);
        }
    }

    /**
     * @deprecated 使用带businessId参数的方法替代
     */
    @Deprecated
    private void handleTencentTextAuditResult(String type, TextAuditingResponse response) throws Exception {
        handleTencentTextAuditResult(type, response, null);
    }



    /**
     * 批量文本审核
     *
     * @param type     检查类型描述
     * @param contents 文本内容列表
     * @param agentId  智能体ID
     * @throws SecurityCheckException 审核异常
     */
    public void batchCheckTextsWithTencentCOS(String type, List<String> contents, Long agentId)
            throws SecurityCheckException {
        if (contents == null || contents.isEmpty()) {
            log.debug("{}文本内容列表为空，跳过批量检查，智能体ID: {}", type, agentId);
            return;
        }

        log.info("开始腾讯云{}文本批量审核，智能体ID: {}，文本数量: {}", type, agentId, contents.size());

        for (int i = 0; i < contents.size(); i++) {
            String content = contents.get(i);
            try {
                checkTextWithTencentCOS(type + "(第" + (i + 1) + "段)", content, agentId);
            } catch (SecurityCheckException e) {
                String batchErrorMsg = errorMessageBuilder.buildBatchCheckErrorMessage(
                        type + "文本", contents.size(), i, e.getMessage());
                log.error("批量文本审核失败，智能体ID: {}，第{}段文本，错误: {}",
                        agentId, i + 1, e.getMessage());
                throw new SecurityCheckException.TencentCloudSecurityCheckException(
                        type + "批量文本", batchErrorMsg, agentId, e);
            }
        }

        log.info("完成腾讯云{}文本批量审核，智能体ID: {}，检查文本数量: {}", type, agentId, contents.size());
    }

}