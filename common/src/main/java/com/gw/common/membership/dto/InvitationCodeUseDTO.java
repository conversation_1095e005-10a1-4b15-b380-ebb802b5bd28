package com.gw.common.membership.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class InvitationCodeUseDTO {
    @Schema(description = "邀请码")
    @NotBlank(message = "邀请码不能为空")
    private String code;
    @Schema(description = "被邀请人的用户名")
    @NotBlank(message = "被邀请人的用户名不能为空")
    private String username;

    public InvitationCodeUseDTO(String code, String username) {
        this.code = code;
        this.username = username;
    }
}
