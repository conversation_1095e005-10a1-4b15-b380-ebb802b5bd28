package com.gw.membership.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

/**
 * 用户会员权益使用记录实体
 */
@Data
@TableName("t_member_benefit_usage")
public class MemberBenefitUsageEntity {
    @TableId(type = AUTO)
    private Long id;
    /**
     * 用户ID
     */
    private String username;

    /**
     * 权益ID
     */
    private Long benefitId;
    private String benefitCode;
    /**
     * 使用次数
     */
    @TableField("use_count")
    private Integer useCount;

    /**
     * 每日限制次数
     */
    private Integer dailyLimit;

    /**
     * 使用日期
     */
    @TableField("use_time")
    private LocalDate useTime;

} 