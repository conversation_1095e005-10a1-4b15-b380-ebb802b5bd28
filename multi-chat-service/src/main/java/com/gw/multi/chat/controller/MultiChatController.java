package com.gw.multi.chat.controller;

import com.gw.common.agent.constant.AgentCommonCacheConstant;
import com.gw.common.agent.constant.StoryConstant;
import com.gw.common.agent.service.AgentProxyService;
import com.gw.common.agent.service.AgentStoryProxyService;
import com.gw.common.agent.vo.AgentBaseVO;
import com.gw.common.agent.vo.AgentStoryBaseVO;
import com.gw.common.agent.vo.AgentStorySceneBaseVO;
import com.gw.common.dto.PageBaseRequest;
import com.gw.common.dto.ResponseResult;
import com.gw.common.membership.constant.MemberBenefitConstant;
import com.gw.common.membership.service.MembershipProxyService;
import com.gw.common.vo.PageBaseContentVo;
import com.gw.common.vo.PaginationVo;
import com.gw.multi.chat.config.CacheProperties;
import com.gw.multi.chat.config.ReactiveUserContextUtil;
import com.gw.multi.chat.dto.SceneSessionResetDTO;
import com.gw.multi.chat.dto.StoryHistoryQueryDTO;
import com.gw.multi.chat.dto.StorySessionQueryDTO;
import com.gw.multi.chat.entity.MultiChatMsg;
import com.gw.multi.chat.entity.MultiChatSession;
import com.gw.multi.chat.service.MultiChatMsgService;
import com.gw.multi.chat.service.MultiChatSessionService;
import com.gw.multi.chat.util.ChatUtils;
import com.gw.multi.chat.vo.ChatHistoryContentVO;
import com.gw.multi.chat.vo.ChatLatestContentVO;
import com.gw.multi.chat.vo.MultiChatSessionCreateVO;
import com.gw.multi.chat.vo.SessionToppingDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

import static com.gw.common.exception.BusinessExceptionCode.BENEFIT_NOT_ALLOWED_CODE;
import static com.gw.common.exception.BusinessExceptionCode.FAIL_CODE;

/**
 * 多智能体聊天控制器
 */
@RestController
@RequestMapping("/api/v1/multi/chat")
@RequiredArgsConstructor
@Log4j2
@Tag(name = "多智能体聊天管理", description = "多智能体群聊会话管理相关接口")
public class MultiChatController {
    private final MembershipProxyService membershipProxyService;
    private final AgentStoryProxyService storyProxyService;
    private final CacheProperties cacheProperties;
    private final MultiChatSessionService multiChatSessionService;
    private final MultiChatMsgService chatMsgService;
    private final AgentProxyService agentProxyService;
    @Operation(summary = "获取用户与指定故事的最近会话",
            description = "根据用户ID和故事ID获取最近创建的多智能体群聊会话，如果不存在则创建新会话",
            responses = {
                    @ApiResponse(responseCode = "200", description = "获取成功"),
                    @ApiResponse(responseCode = "400", description = "参数错误"),
                    @ApiResponse(responseCode = "500", description = "服务器内部错误")
            })
    @PostMapping("/session/latest")
    public Mono<ResponseResult<MultiChatSessionCreateVO>> getLatestSessionByStory(@RequestBody @Valid StorySessionQueryDTO params) {
        return ReactiveUserContextUtil.getCurrentUsername()
                .flatMap(username -> handleGetLatestSessionByStory(params, username));
    }

    private Mono<ResponseResult<MultiChatSessionCreateVO>> handleGetLatestSessionByStory(StorySessionQueryDTO params, String username) {
        try {
            log.info("获取用户 {} 与故事 {} 的最近会话, scendId: {}", username, params.getStoryId(), params.getSceneId());

            // 获取或创建会话
            MultiChatSession session = multiChatSessionService.getOrCreateStorySession(
                    username,
                    params.getStoryId()
                    , params.getSceneId()
            );

            if (session == null) {
                log.error("创建会话失败");
                return Mono.just(ResponseResult.failure(FAIL_CODE, "创建会话失败"));
            }
            var sceneSession = session.getSceneMap().get(params.getSceneId());
            MultiChatSessionCreateVO result = new MultiChatSessionCreateVO(
                    sceneSession.getSceneSessionId(),
                    session.getStoryId(),
                    session.getCurrentSceneId()
            );
            storyProxyService.recCurrentSceneUse(params.getStoryId(), params.getSceneId(), username);
            log.info("成功获取会话: {}", sceneSession.getSceneSessionId());
            return Mono.just(ResponseResult.success(result));

        } catch (Exception e) {
            log.error("获取最近会话信息失败", e);
            return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
        }
    }

    private AgentStorySceneBaseVO getAndValidateScene(Long storyId, Long sceneId, String username) {
        String storyCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_MAP_CACHE_KEY);
        String sceneCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_SCENE_KEY);

        AgentStoryBaseVO vo = storyProxyService.getStoryInfo(storyCacheKey, storyId);
        if (vo == null) {
            log.error("故事不存在: {}", storyId);
            throw new RuntimeException("故事不存在: " + storyId);
        } else if (vo.getShelfStatus() != StoryConstant.SHELF_ON_STATUS) {
            log.error("故事未上上架，请联系管理员: {}", storyId);
            throw new RuntimeException("故事未上上架，请联系管理员: " + storyId);
        } else if (vo.getStatus() != StoryConstant.STORY_STATUS_PUBLISHED) {
            log.error("故事未发布，请联系管理员: {}", storyId);
            throw new RuntimeException("故事未发布，请联系管理员: " + storyId);
        }
        AgentStorySceneBaseVO sceneVo = storyProxyService.getStorySceneInfo(sceneId, sceneCacheKey);

        if (sceneVo == null) {
            log.error("剧情章节校验失败: 未找到剧情章节信息, 用户: {}, 故事ID: {}, 剧情章节ID: {}",
                    username, storyId, sceneId);
            throw new RuntimeException("剧情章节校验失败: 未找到剧情章节信息, 用户: " + username + ", 故事ID: " + storyId + ", 故事章节ID: " + sceneId);
        }
        return sceneVo;
    }

    private Mono<ResponseResult<MultiChatSessionCreateVO>> handleResetSession(SceneSessionResetDTO params, String username) {

        try {
            var allow = membershipProxyService.checkBenefitCanUse(username, MemberBenefitConstant.MEMORY_RESET_CODE);
            if (!allow) {
                return Mono.just(ResponseResult.failure(BENEFIT_NOT_ALLOWED_CODE, "没有权限重置会话"));
            }
            AgentStorySceneBaseVO story = getAndValidateScene(params.getStoryId(), params.getSceneId(), username);
            if (story == null) {
                return Mono.just(ResponseResult.failure(FAIL_CODE, "故事或者章节不存在或无权访问"));
            }

            multiChatSessionService.deleteAllSessionBySceneId(params.getStoryId(), params.getSceneId(), username);

            var ret =  handleGetLatestSessionByStory(new StorySessionQueryDTO(params.getStoryId(), params.getSceneId()), username);
            log.info("成功重置会话: {}", ret);
            return ret;
        } catch (Exception e) {
            log.error("重置会话失败", e);
            return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
        }
    }

    @Operation(summary = "重置会话")
    @PostMapping("/reset_session")
    public Mono<ResponseResult<MultiChatSessionCreateVO>> resetSession(@RequestBody @Valid SceneSessionResetDTO params) {
        return ReactiveUserContextUtil.getCurrentUsername()
                .flatMap(username -> handleResetSession(params, username));
    }
    private Mono<ResponseResult<?>> handleSessionTopping(SessionToppingDTO params, String username) {
        try {
            multiChatSessionService.sessionTopping(params.getSessionId(), params.getTopping(), username);
            return Mono.just(ResponseResult.success(null));
        } catch (Exception e) {
            log.error("置顶会话失败", e);
            return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
        }
    }
    @Operation(summary = "置顶会话")
    @PostMapping("/chat/session_topping")
    public Mono<ResponseResult<?>> sessionTopping(@RequestBody @Valid SessionToppingDTO params) {
        return ReactiveUserContextUtil.getCurrentUsername()
                .flatMap(username -> handleSessionTopping(params, username));
    }
    private PageRequest createPageRequest(PageBaseRequest<?> params) {
        int page = params.getCurrent() - 1;
        int size = params.getPageSize();
        return PageRequest.of(page, size, Sort.by(Sort.Order.desc("isPinned"), Sort.Order.desc("lastMessageTime")));
    }
    private Mono<ResponseResult<Page<ChatLatestContentVO>>> handleGetSessionsLatestChatByUser(PageBaseRequest<?> params, String username) {
        try {
            PageRequest pageRequest = createPageRequest(params);
            Page<MultiChatSession> sessions = multiChatSessionService.handleGetSessionsLatestChatByUser(username, pageRequest);
            List<Long> agentIds = sessions.stream().map(MultiChatSession::getLastChatAgentId).toList();
            String cacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_MAP_CACHE_KEY);
            Map<Long, AgentBaseVO> agentMap = agentProxyService.getAgentsByIds(cacheKey, agentIds);
            // 转换为ChatLatestContentVO
            Page<ChatLatestContentVO> result = sessions.map(item -> convertToLatestContentVO(item, agentMap));

            log.info("成功获取用户 {} 的群聊会话列表，共 {} 条", username, result.getTotalElements());
            return Mono.just(ResponseResult.success(result));
        } catch (Exception e) {
            log.error("获取用户群聊会话列表失败", e);
            return Mono.just(ResponseResult.failure(FAIL_CODE, e.getMessage()));
        }
    }
    @Operation(summary = "获取我的群聊的对话历史")
    @PostMapping("/messages/history")
    public Mono<ResponseResult<PageBaseContentVo<ChatHistoryContentVO>>> getSessionMessages(
            @RequestBody @Valid PageBaseRequest<StoryHistoryQueryDTO> params) {
        return ReactiveUserContextUtil.getCurrentUsername()
                .flatMap(username -> handleGetSessionMessages(params, username));
    }

    private Mono<ResponseResult<PageBaseContentVo<ChatHistoryContentVO>>> handleGetSessionMessages(
            PageBaseRequest<StoryHistoryQueryDTO> params, String username) {
        int page = params.getCurrent() - 1;
        int size = params.getPageSize();

        MultiChatSession session = multiChatSessionService.getOrCreateStorySession(
                username,
                params.getFilter().getStoryId()
                , params.getFilter().getSceneId()
        );
        if (session == null) {
            return Mono.just(ResponseResult.failure(FAIL_CODE, "会话不存在"));
        }
        MultiChatSession.SceneSession sceneSession = session.getSceneMap().get(params.getFilter().getSceneId());
        if (sceneSession == null) {
            return Mono.just(ResponseResult.failure(FAIL_CODE, "会话不存在"));
        }
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "updateTime"));
        Page<MultiChatMsg> messages = chatMsgService.pageBySceneSessionIdAndStoryIdAndSceneId(sceneSession.getSceneSessionId(),
                params.getFilter().getStoryId(), params.getFilter().getSceneId(), pageRequest);

        PageBaseContentVo<ChatHistoryContentVO> resp = new PageBaseContentVo<>();
        
        // 转换MultiChatMsg到ChatHistoryContentVO
        List<ChatHistoryContentVO> historyList = messages.getContent().stream()
                .map(this::convertToHistoryContentVO)
                .toList();
        
        resp.setList(historyList);
        resp.setPagination(new PaginationVo(messages.getTotalElements(), messages.getNumber() + 1, messages.getSize()));
        return Mono.just(ResponseResult.success(resp));
    }
    @Operation(summary = "分页获取当前用户的群聊最新会话内容", description = "分页获取当前用户的群聊最新会话内容")
    @PostMapping("/sessions/latest/chat")
    public Mono<ResponseResult<Page<ChatLatestContentVO>>> getSessionsLatestChatByUser(@RequestBody @Valid PageBaseRequest<?> params) {
        return ReactiveUserContextUtil.getCurrentUsername()
                .flatMap(username -> handleGetSessionsLatestChatByUser(params, username));
    }

    /**
     * 将MultiChatSession转换为ChatLatestContentVO
     */
    private ChatLatestContentVO convertToLatestContentVO(MultiChatSession session, Map<Long, AgentBaseVO> agentMap) {
        ChatLatestContentVO vo = ChatLatestContentVO.builder()
                .sessionId(session.getSessionId())
                .storyId(session.getStoryId())
                .sceneId(session.getCurrentSceneId())
                .chatContent(session.getLastMessage())
                .lastChatAgentId(session.getLastChatAgentId())
                .topping(session.getIsPinned()?1:0)
                .lastAccessTime(session.getLastAccessTime())
                .build();

        // 设置更新时间（使用最后消息时间或最后访问时间）
        if (session.getLastMessageTime() != null) {

            vo.setUpdateTime(java.time.LocalDateTime.ofEpochSecond(
                    session.getLastMessageTime(), 0, java.time.ZoneOffset.UTC));
            vo.setLastAccessTime(vo.getUpdateTime());
        } else if (session.getLastAccessTime() != null) {
            vo.setUpdateTime(session.getLastAccessTime());
        }
        // 获取故事和场景信息
        try {
            String storyCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_MAP_CACHE_KEY);
            AgentStoryBaseVO storyInfo = storyProxyService.getStoryInfo(storyCacheKey, session.getStoryId());
            if (storyInfo != null) {
                vo.setStoryName(storyInfo.getName());
            }

            String sceneCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_SCENE_KEY);
            AgentStorySceneBaseVO sceneInfo = storyProxyService.getStorySceneInfo(session.getCurrentSceneId(), sceneCacheKey);
            if (sceneInfo != null) {
                vo.setSceneName(sceneInfo.getSceneName());
                vo.setSceneBgUrl(sceneInfo.getBgThumbnailUrl());
            }

            // 获取最后发言智能体的名称
//            log.info("获取最后发言智能体的名称: {}", session.getLastChatAgentId());
            if (session.getLastChatAgentId() != null && session.getLastChatAgentId() > 0) {
                var agentVo = agentMap.get(session.getLastChatAgentId());
                if(agentVo != null){
                    vo.setChatAgentName(agentVo.getName());
                }else if (session.getSceneMap() != null && session.getCurrentSceneId() != null) {

                    MultiChatSession.SceneSession sceneSession = session.getSceneMap().get(session.getCurrentSceneId());
                    if (sceneSession != null && sceneSession.getAgents() != null) {
                        MultiChatSession.MultiChatAgent agent = sceneSession.getAgents().get(session.getLastChatAgentId());
                        if (agent != null) {
                            vo.setChatAgentName(agent.getAgentName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取故事或场景信息失败: {}", e.getMessage());
        }

        return vo;
    }

    /**
     * 将MultiChatMsg转换为ChatHistoryContentVO
     */
    private ChatHistoryContentVO convertToHistoryContentVO(MultiChatMsg message) {
        // 移除content中的心理内容（括号内容）
        String cleanContent = ChatUtils.removeBracketContent(message.getContent());

        return ChatHistoryContentVO.builder()
                .chatId(message.getId())
                .content(cleanContent)
                .role(message.getRole())
                .agentId(message.getAgentId())
                .updateTime(message.getCreateTime())
                .build();
    }
}